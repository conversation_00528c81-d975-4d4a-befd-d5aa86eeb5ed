"""
HTML entity decoder utility for PACE application.
This module provides functions to decode HTML entities in text.
"""

import re
import html

def decode_html_entities(text):
    """
    Decode HTML entities in text.
    
    Args:
        text (str): Text containing HTML entities
        
    Returns:
        str: Text with HTML entities decoded
    """
    if not isinstance(text, str):
        return text
        
    # Decode HTML entities using the html module
    decoded_text = html.unescape(text)
    
    # Handle specific entities that might not be caught
    entity_map = {
        '&#x27;': "'",  # Apostrophe
        '&apos;': "'",  # Apostrophe
        '&quot;': '"',  # Double quote
        '&amp;': '&',   # Ampersand
        '&lt;': '<',    # Less than
        '&gt;': '>',    # Greater than
    }
    
    for entity, char in entity_map.items():
        decoded_text = decoded_text.replace(entity, char)
    
    return decoded_text

def process_placeholders(placeholders):
    """
    Process placeholders to decode HTML entities.
    
    Args:
        placeholders (dict): Dictionary of placeholders and values
        
    Returns:
        dict: Dictionary with HTML entities decoded in values
    """
    processed = {}
    
    for key, value in placeholders.items():
        if isinstance(value, str):
            processed[key] = decode_html_entities(value)
        else:
            processed[key] = value
            
    return processed
