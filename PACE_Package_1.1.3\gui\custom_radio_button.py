"""
Custom radio button implementation for PACE.
"""

from PySide6.QtWidgets import QPushButton
from PySide6.QtCore import Signal

class CustomRadioButton(QPushButton):
    """
    A custom radio button implementation using QPushButton.
    """

    # Signal emitted when the button is selected
    selected = Signal()

    def __init__(self, text, parent=None):
        """
        Initialize the custom radio button.

        Args:
            text (str): The text to display on the button
            parent: The parent widget
        """
        super().__init__(text, parent)

        # Make the button checkable
        self.setCheckable(True)

        # Set the initial state
        self.setChecked(False)

        # Connect the clicked signal
        self.clicked.connect(self._on_clicked)

        # Apply styling
        self.setStyleSheet("""
            CustomRadioButton {
                padding: 8px 16px;
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: #f8f8f8;
                color: #333;
                font-weight: normal;
            }

            CustomRadioButton:checked {
                background-color: #4a6fa5;
                color: white;
                font-weight: bold;
                border: 1px solid #3a5f95;
            }

            CustomRadioButton:hover {
                background-color: #e8e8e8;
            }

            CustomRadioButton:checked:hover {
                background-color: #3a5f95;
            }
        """)

    def _on_clicked(self, checked=False):
        """
        Handle the button click.

        Args:
            checked (bool): Whether the button is checked
        """
        # Force the button to be checked
        self.setChecked(True)

        # Emit the selected signal
        self.selected.emit()
