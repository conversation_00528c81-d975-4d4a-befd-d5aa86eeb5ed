"""
Style manager for the PACE application.
"""

class StyleManager:
    """
    Class for managing application styles and themes.
    """

    # Light theme colors - Modern, softer palette
    LIGHT_THEME = {
        "primary": "#5e81ac",         # Soft blue
        "secondary": "#81a1c1",      # Lighter blue
        "accent": "#4a6fa5",         # Darker blue
        "background": "#f5f7fa",     # Very light blue-gray
        "card_background": "#ffffff", # White
        "text": "#2e3440",           # Dark gray with blue undertone
        "text_secondary": "#4c566a", # Medium gray
        "border": "#d8dee9",         # Light gray
        "success": "#a3be8c",        # Soft green
        "warning": "#ebcb8b",        # Soft yellow
        "error": "#bf616a"           # Soft red
    }

    # Dark theme colors - Modern, softer palette
    DARK_THEME = {
        "primary": "#4a6fa5",         # Softer blue
        "secondary": "#5e81ac",      # Lighter blue
        "accent": "#81a1c1",         # Pale blue
        "background": "#2e3440",     # Dark gray with blue undertone
        "card_background": "#3b4252", # Slightly lighter than background
        "text": "#eceff4",           # Off-white
        "text_secondary": "#d8dee9", # Light gray
        "border": "#4c566a",         # Medium gray
        "success": "#a3be8c",        # Soft green
        "warning": "#ebcb8b",        # Soft yellow
        "error": "#bf616a"           # Soft red
    }

    @classmethod
    def get_light_stylesheet(cls):
        """
        Get the light theme stylesheet.

        Returns:
            str: The light theme stylesheet
        """
        colors = cls.LIGHT_THEME
        return f"""
        /* Global Styles */
        QWidget {{
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
            color: {colors['text']};
            background-color: {colors['background']};
        }}

        /* Main Window */
        QMainWindow {{
            background-color: {colors['background']};
        }}

        /* Header */
        #header_widget {{
            background-color: {colors['background']};
            color: {colors['text']};
            padding: 10px;
            border-radius: 0px;
        }}

        #header_label {{
            font-size: 18pt;
            font-weight: bold;
            color: {colors['text']};
        }}

        #subtitle_label {{
            color: {colors['text_secondary']};
        }}

        #version_label {{
            color: {colors['text_secondary']};
            font-size: 9pt;
            margin-left: 5px;
        }}

        /* Buttons */
        QPushButton {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }}

        QPushButton:hover {{
            background-color: {colors['secondary']};
        }}

        QPushButton:pressed {{
            background-color: {colors['accent']};
        }}

        QPushButton:disabled {{
            background-color: {colors['border']};
            color: {colors['text_secondary']};
        }}

        /* Primary Button */
        QPushButton#primary_button {{
            background-color: {colors['accent']};
            color: white;
        }}

        QPushButton#primary_button:hover {{
            background-color: {colors['secondary']};
        }}

        QPushButton#primary_button:pressed {{
            background-color: {colors['primary']};
        }}

        /* Input Fields */
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
            border: 1px solid {colors['border']};
            border-radius: 6px;
            padding: 8px;
            background-color: {colors['card_background']};
            color: {colors['text']};
            selection-background-color: {colors['secondary']};
        }}

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
            border: 1px solid {colors['accent']};
            background-color: {colors['card_background']};
        }}

        /* Combo Box */
        QComboBox {{
            padding-right: 20px;
        }}

        QComboBox::drop-down {{
            subcontrol-origin: padding;
            subcontrol-position: right center;
            width: 20px;
            border-left: 1px solid {colors['border']};
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }}

        QComboBox::down-arrow {{
            image: url(resources/icons/down_arrow.png);
        }}

        QComboBox QAbstractItemView {{
            border: 1px solid {colors['border']};
            border-radius: 6px;
            selection-background-color: {colors['accent']};
            selection-color: white;
            background-color: {colors['card_background']};
            color: {colors['text']};
        }}

        /* Checkbox and Radio Button */
        QCheckBox, QRadioButton {{
            spacing: 8px;
            color: {colors['text']};
        }}

        QCheckBox::indicator, QRadioButton::indicator {{
            width: 18px;
            height: 18px;
        }}

        QCheckBox::indicator:checked {{
            background-color: {colors['accent']};
            border: 2px solid {colors['accent']};
            border-radius: 3px;
        }}

        QRadioButton::indicator:checked {{
            background-color: {colors['accent']};
            border: 2px solid {colors['accent']};
            border-radius: 9px;
        }}

        QCheckBox::indicator:unchecked {{
            background-color: {colors['card_background']};
            border: 2px solid {colors['border']};
            border-radius: 3px;
        }}

        QRadioButton::indicator:unchecked {{
            background-color: {colors['card_background']};
            border: 2px solid {colors['border']};
            border-radius: 9px;
        }}

        /* Group Box */
        QGroupBox {{
            border: 1px solid {colors['border']};
            border-radius: 8px;
            margin-top: 20px;
            font-weight: bold;
            color: {colors['text']};
            background-color: {colors['card_background']};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top left;
            left: 10px;
            padding: 0 5px;
            color: {colors['text_secondary']};
        }}

        /* Tabs */
        QTabWidget::pane {{
            border: 1px solid {colors['border']};
            border-radius: 8px;
            background-color: {colors['card_background']};
        }}

        QTabBar::tab {{
            background-color: {colors['background']};
            border: 1px solid {colors['border']};
            border-bottom: none;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 8px 16px;
            margin-right: 2px;
            color: {colors['text_secondary']};
        }}

        QTabBar::tab:selected {{
            background-color: {colors['card_background']};
            border-bottom: none;
            color: {colors['text']};
        }}

        QTabBar::tab:!selected {{
            margin-top: 2px;
        }}

        /* Scroll Bar */
        QScrollBar:vertical {{
            border: none;
            background-color: transparent;
            width: 12px;
            margin: 0;
        }}

        QScrollBar::handle:vertical {{
            background-color: {colors['border']};
            border-radius: 6px;
            min-height: 30px;
        }}

        QScrollBar::handle:vertical:hover {{
            background-color: {colors['accent']};
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0;
            width: 0;
        }}

        QScrollBar:horizontal {{
            border: none;
            background-color: transparent;
            height: 12px;
            margin: 0;
        }}

        QScrollBar::handle:horizontal {{
            background-color: {colors['border']};
            border-radius: 6px;
            min-width: 30px;
        }}

        QScrollBar::handle:horizontal:hover {{
            background-color: {colors['accent']};
        }}

        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            height: 0;
            width: 0;
        }}

        /* Menu */
        QMenuBar {{
            background-color: {colors['background']};
            color: {colors['text']};
            border-bottom: 1px solid {colors['border']};
        }}

        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 16px;
            border-radius: 4px;
            margin: 2px;
        }}

        QMenuBar::item:selected {{
            background-color: {colors['accent']};
            color: white;
        }}

        QMenu {{
            background-color: {colors['card_background']};
            border: 1px solid {colors['border']};
            border-radius: 8px;
            color: {colors['text']};
            padding: 5px;
        }}

        QMenu::item {{
            padding: 8px 16px;
            border-radius: 4px;
            margin: 2px;
        }}

        QMenu::item:selected {{
            background-color: {colors['accent']};
            color: white;
        }}

        /* Status Bar */
        QStatusBar {{
            background-color: {colors['background']};
            color: {colors['text']};
            border-top: 1px solid {colors['border']};
        }}

        /* Progress Bar */
        QProgressBar {{
            border: 1px solid {colors['border']};
            border-radius: 6px;
            text-align: center;
            background-color: {colors['background']};
            color: {colors['text']};
        }}

        QProgressBar::chunk {{
            background-color: {colors['accent']};
            border-radius: 5px;
            margin: 0.5px;
        }}

        /* Slider */
        QSlider::groove:horizontal {{
            border: 1px solid {colors['border']};
            height: 8px;
            background-color: {colors['background']};
            margin: 2px 0;
            border-radius: 4px;
        }}

        QSlider::handle:horizontal {{
            background-color: {colors['accent']};
            border: 1px solid {colors['accent']};
            width: 18px;
            margin: -6px 0;
            border-radius: 9px;
        }}

        QSlider::handle:horizontal:hover {{
            background-color: {colors['secondary']};
            border: 1px solid {colors['secondary']};
        }}

        /* Tooltip */
        QToolTip {{
            border: 1px solid {colors['border']};
            background-color: {colors['card_background']};
            color: {colors['text']};
            padding: 6px;
            border-radius: 4px;
        }}

        /* Theme Switch */
        #theme_switch {{
            background-color: {colors['accent']};
            color: white;
            border: none;
            padding: 6px;
            border-radius: 15px;
            font-size: 14pt;
        }}

        #theme_switch:hover {{
            background-color: {colors['secondary']};
        }}
        """

    @classmethod
    def get_dark_stylesheet(cls):
        """
        Get the dark theme stylesheet.

        Returns:
            str: The dark theme stylesheet
        """
        colors = cls.DARK_THEME
        return f"""
        /* Global Styles */
        QWidget {{
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
            color: {colors['text']};
            background-color: {colors['background']};
        }}

        /* Main Window */
        QMainWindow {{
            background-color: {colors['background']};
        }}

        /* Header */
        #header_widget {{
            background-color: {colors['background']};
            color: {colors['text']};
            padding: 10px;
            border-radius: 0px;
        }}

        #header_label {{
            font-size: 18pt;
            font-weight: bold;
            color: {colors['text']};
        }}

        #subtitle_label {{
            color: {colors['text_secondary']};
        }}

        #version_label {{
            color: {colors['text_secondary']};
            font-size: 9pt;
            margin-left: 5px;
        }}

        /* Buttons */
        QPushButton {{
            background-color: {colors['primary']};
            color: {colors['text']};
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }}

        QPushButton:hover {{
            background-color: {colors['secondary']};
        }}

        QPushButton:pressed {{
            background-color: {colors['accent']};
        }}

        QPushButton:disabled {{
            background-color: {colors['card_background']};
            color: {colors['text_secondary']};
        }}

        /* Primary Button */
        QPushButton#primary_button {{
            background-color: {colors['accent']};
            color: {colors['text']};
        }}

        QPushButton#primary_button:hover {{
            background-color: {colors['secondary']};
        }}

        QPushButton#primary_button:pressed {{
            background-color: {colors['primary']};
        }}

        /* Input Fields */
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
            border: 1px solid {colors['border']};
            border-radius: 6px;
            padding: 8px;
            background-color: {colors['card_background']};
            color: {colors['text']};
            selection-background-color: {colors['primary']};
        }}

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
            border: 1px solid {colors['accent']};
            background-color: {colors['background']};
        }}

        /* Combo Box */
        QComboBox {{
            padding-right: 20px;
        }}

        QComboBox::drop-down {{
            subcontrol-origin: padding;
            subcontrol-position: right center;
            width: 20px;
            border-left: 1px solid {colors['border']};
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }}

        QComboBox::down-arrow {{
            image: url(resources/icons/down_arrow_dark.png);
        }}

        QComboBox QAbstractItemView {{
            border: 1px solid {colors['border']};
            border-radius: 6px;
            selection-background-color: {colors['accent']};
            selection-color: {colors['text']};
            background-color: {colors['card_background']};
            color: {colors['text']};
        }}

        /* Checkbox and Radio Button */
        QCheckBox, QRadioButton {{
            spacing: 8px;
            color: {colors['text']};
        }}

        QCheckBox::indicator, QRadioButton::indicator {{
            width: 18px;
            height: 18px;
        }}

        QCheckBox::indicator:checked {{
            background-color: {colors['accent']};
            border: 2px solid {colors['accent']};
            border-radius: 3px;
        }}

        QRadioButton::indicator:checked {{
            background-color: {colors['accent']};
            border: 2px solid {colors['accent']};
            border-radius: 9px;
        }}

        QCheckBox::indicator:unchecked {{
            background-color: {colors['card_background']};
            border: 2px solid {colors['border']};
            border-radius: 3px;
        }}

        QRadioButton::indicator:unchecked {{
            background-color: {colors['card_background']};
            border: 2px solid {colors['border']};
            border-radius: 9px;
        }}

        /* Group Box */
        QGroupBox {{
            border: 1px solid {colors['border']};
            border-radius: 8px;
            margin-top: 20px;
            font-weight: bold;
            color: {colors['text']};
            background-color: {colors['card_background']};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top left;
            left: 10px;
            padding: 0 5px;
            color: {colors['text_secondary']};
        }}

        /* Tabs */
        QTabWidget::pane {{
            border: 1px solid {colors['border']};
            border-radius: 8px;
            background-color: {colors['card_background']};
        }}

        QTabBar::tab {{
            background-color: {colors['background']};
            border: 1px solid {colors['border']};
            border-bottom: none;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 8px 16px;
            margin-right: 2px;
            color: {colors['text_secondary']};
        }}

        QTabBar::tab:selected {{
            background-color: {colors['card_background']};
            border-bottom: none;
            color: {colors['text']};
        }}

        QTabBar::tab:!selected {{
            margin-top: 2px;
        }}

        /* Scroll Bar */
        QScrollBar:vertical {{
            border: none;
            background-color: transparent;
            width: 12px;
            margin: 0;
        }}

        QScrollBar::handle:vertical {{
            background-color: {colors['border']};
            border-radius: 6px;
            min-height: 30px;
        }}

        QScrollBar::handle:vertical:hover {{
            background-color: {colors['accent']};
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0;
            width: 0;
        }}

        QScrollBar:horizontal {{
            border: none;
            background-color: transparent;
            height: 12px;
            margin: 0;
        }}

        QScrollBar::handle:horizontal {{
            background-color: {colors['border']};
            border-radius: 6px;
            min-width: 30px;
        }}

        QScrollBar::handle:horizontal:hover {{
            background-color: {colors['accent']};
        }}

        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            height: 0;
            width: 0;
        }}

        /* Menu */
        QMenuBar {{
            background-color: {colors['background']};
            color: {colors['text']};
            border-bottom: 1px solid {colors['border']};
        }}

        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 16px;
            border-radius: 4px;
            margin: 2px;
        }}

        QMenuBar::item:selected {{
            background-color: {colors['accent']};
            color: {colors['text']};
        }}

        QMenu {{
            background-color: {colors['card_background']};
            border: 1px solid {colors['border']};
            border-radius: 8px;
            color: {colors['text']};
            padding: 5px;
        }}

        QMenu::item {{
            padding: 8px 16px;
            border-radius: 4px;
            margin: 2px;
        }}

        QMenu::item:selected {{
            background-color: {colors['accent']};
            color: {colors['text']};
        }}

        /* Status Bar */
        QStatusBar {{
            background-color: {colors['background']};
            color: {colors['text']};
            border-top: 1px solid {colors['border']};
        }}

        /* Progress Bar */
        QProgressBar {{
            border: 1px solid {colors['border']};
            border-radius: 6px;
            text-align: center;
            background-color: {colors['background']};
            color: {colors['text']};
        }}

        QProgressBar::chunk {{
            background-color: {colors['accent']};
            border-radius: 5px;
            margin: 0.5px;
        }}

        /* Slider */
        QSlider::groove:horizontal {{
            border: 1px solid {colors['border']};
            height: 8px;
            background-color: {colors['background']};
            margin: 2px 0;
            border-radius: 4px;
        }}

        QSlider::handle:horizontal {{
            background-color: {colors['accent']};
            border: 1px solid {colors['accent']};
            width: 18px;
            margin: -6px 0;
            border-radius: 9px;
        }}

        QSlider::handle:horizontal:hover {{
            background-color: {colors['secondary']};
            border: 1px solid {colors['secondary']};
        }}

        /* Tooltip */
        QToolTip {{
            border: 1px solid {colors['border']};
            background-color: {colors['card_background']};
            color: {colors['text']};
            padding: 6px;
            border-radius: 4px;
        }}

        /* Theme Switch */
        #theme_switch {{
            background-color: {colors['accent']};
            color: {colors['text']};
            border: none;
            padding: 6px;
            border-radius: 15px;
            font-size: 14pt;
        }}

        #theme_switch:hover {{
            background-color: {colors['secondary']};
        }}
        """
