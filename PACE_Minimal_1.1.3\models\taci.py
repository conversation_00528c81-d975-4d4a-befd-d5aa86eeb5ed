"""
Threat Actor Communications and Intelligence (TACI) engagement model for PACE application.
"""

from models.engagement import Engagement
from data.carriers import get_carrier_rate

class TACIEngagement(Engagement):
    """
    Class representing a Threat Actor Communications and Intelligence (TACI) engagement in the PACE application.
    """

    def __init__(self, client=None):
        """
        Initialize a new TACIEngagement object.

        Args:
            client (Client): The client for this engagement
        """
        super().__init__(client, "TACI")
        self.is_ransom_communications = False
        self.is_data_download = False
        self.is_ransom_site_monitoring = False
        self.data_download_hours_min = 0
        self.data_download_hours_max = 0
        self.ransom_site_monitoring_hours_min = 0
        self.ransom_site_monitoring_hours_max = 0
        self.is_single_price = True  # Always use single price model (no ranges)
        self.is_fixed_fee = False    # Track if it's fixed fee pricing
        self.needs_msa = False

    def to_dict(self):
        """
        Convert the TACI engagement data to a dictionary.

        Returns:
            dict: A dictionary containing the TACI engagement data
        """
        data = super().to_dict()
        data.update({
            "is_ransom_communications": self.is_ransom_communications,
            "is_data_download": self.is_data_download,
            "is_ransom_site_monitoring": self.is_ransom_site_monitoring,
            "data_download_hours_min": self.data_download_hours_min,
            "data_download_hours_max": self.data_download_hours_max,
            "ransom_site_monitoring_hours_min": self.ransom_site_monitoring_hours_min,
            "ransom_site_monitoring_hours_max": self.ransom_site_monitoring_hours_max,
            "is_single_price": self.is_single_price,
            "is_fixed_fee": self.is_fixed_fee,
            "needs_msa": self.needs_msa
        })
        return data

    @classmethod
    def from_dict(cls, data, client=None):
        """
        Create a TACIEngagement object from a dictionary.

        Args:
            data (dict): A dictionary containing TACI engagement data
            client (Client): The client for this engagement

        Returns:
            TACIEngagement: A new TACIEngagement object
        """
        engagement = super().from_dict(data, client)

        engagement.is_ransom_communications = data.get("is_ransom_communications", False)
        engagement.is_data_download = data.get("is_data_download", False)
        engagement.is_ransom_site_monitoring = data.get("is_ransom_site_monitoring", False)
        engagement.data_download_hours_min = data.get("data_download_hours_min", 0)
        engagement.data_download_hours_max = data.get("data_download_hours_max", 0)
        engagement.ransom_site_monitoring_hours_min = data.get("ransom_site_monitoring_hours_min", 0)
        engagement.ransom_site_monitoring_hours_max = data.get("ransom_site_monitoring_hours_max", 0)
        engagement.is_single_price = data.get("is_single_price", True)  # Default to single price
        engagement.is_fixed_fee = data.get("is_fixed_fee", False)       # Default to hourly
        engagement.needs_msa = data.get("needs_msa", False)

        return engagement

    def is_valid(self):
        """
        Check if the TACI engagement data is valid.

        Returns:
            bool: True if the TACI engagement data is valid, False otherwise
        """
        if not super().is_valid():
            return False

        # At least one type of TACI service must be selected
        if not (self.is_ransom_communications or self.is_data_download or self.is_ransom_site_monitoring):
            return False

        # If data download is selected, hours must be specified
        if self.is_data_download and (self.data_download_hours_min <= 0 or self.data_download_hours_max <= 0):
            return False

        # If ransom site monitoring is selected, hours must be specified
        if self.is_ransom_site_monitoring and (self.ransom_site_monitoring_hours_min <= 0 or self.ransom_site_monitoring_hours_max <= 0):
            return False

        return True

    def get_document_folder_name(self):
        """
        Get the name of the folder where documents for this engagement should be saved.

        Returns:
            str: The folder name
        """
        if not self.client or not self.client.name:
            return "Unnamed Client - Scoping Documents Threat Actor Communications"

        if self.is_ransom_communications:
            return f"{self.client.name} - Scoping Documents Threat Actor Communications"
        elif self.is_data_download:
            return f"{self.client.name} - Scoping Documents Data Downloads"
        elif self.is_ransom_site_monitoring:
            return f"{self.client.name} - Scoping Documents Ransom Site Monitoring"
        else:
            return f"{self.client.name} - Scoping Documents Threat Actor Communications"

    def get_sow_template_path(self):
        """
        Get the path to the SOW template for this engagement.

        Returns:
            str: The path to the SOW template
        """
        # Use the appropriate template based on the engagement type
        if self.is_ransom_communications:
            if self.client and self.client.law_firm == "The Beckage Firm":
                return "templates/base_templates/taci/SOW-RN-Template-Beckage Firm- Ransom Consulting.docx"
            elif self.client and self.client.insurance_carrier == "Chubb":
                return "templates/base_templates/taci/SOW-RN-Template-Chubb Only_Ransom Consulting.docx"
            elif self.client and self.client.insurance_carrier == "Coalition" or self.is_fixed_fee:
                # Use Fixed Fee Pricing template for Coalition or when fixed fee is explicitly selected
                return "templates/base_templates/taci/SOW_Coalition_Ransom_Consulting_FFP_template.docx"
            else:
                return "templates/base_templates/taci/SOW_Ransom_Consulting_template.docx"
        elif self.is_data_download:
            if self.client and self.client.insurance_carrier == "Chubb":
                return "templates/base_templates/taci/SOW-RN-Template_ChubbOnly_Client Data Download.docx"
            else:
                return "templates/base_templates/taci/SOW-RN-Template_Client Data Download.docx"
        elif self.is_ransom_site_monitoring:
            return "templates/base_templates/taci/SOW-RN-Template_Ransom Site Monitoring.docx"
        else:
            # Default to ransom communications template
            return "templates/base_templates/taci/SOW_Ransom_Consulting_template.docx"

    def get_taci_sow_template_path(self):
        """
        Get the path to the TACI SOW template for this engagement.

        Returns:
            str: The path to the TACI SOW template
        """
        return self.get_sow_template_path()

    def get_sow_filename(self):
        """
        Get the filename for the SOW document.

        Returns:
            str: The filename for the SOW document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Ransom Communications - SOW {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Ransom Communications - SOW {datetime.now().strftime('%Y%m%d')}.docx"

    def calculate_pricing(self):
        """
        Calculate pricing for this TACI engagement.

        Returns:
            dict: A dictionary containing pricing information
        """
        pricing = {}

        # Get the hourly rate for TACI
        if self.client and self.client.insurance_carrier:
            pricing["taci_rate"] = get_carrier_rate(self.client.insurance_carrier, "TACI")
        else:
            pricing["taci_rate"] = 450.0  # Default rate

        # Format the taci_rate with a dollar sign for display in documents
        pricing["taci_rate_formatted"] = f"${int(pricing['taci_rate'])}"

        if self.is_ransom_communications:
            return self._calculate_ransom_communications_pricing(pricing)
        elif self.is_data_download:
            return self._calculate_data_download_pricing(pricing)
        elif self.is_ransom_site_monitoring:
            return self._calculate_ransom_site_monitoring_pricing(pricing)
        else:
            return pricing

    def _calculate_ransom_communications_pricing(self, pricing):
        """
        Calculate pricing for ransom communications.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Get the hourly rate
        hourly_rate = pricing['taci_rate']

        # Use the formatted rate for display in documents
        pricing['taci_rate'] = pricing['taci_rate_formatted']

        if self.client and self.client.law_firm == "The Beckage Firm":
            # Beckage-specific pricing - use high end of range
            phase1_hours = 15  # High end of range (was 5-15)
            phase2_hours = 25  # High end of range (was 5-25)

            # Single price model - no ranges

            # Use single price (high end of range)
            pricing["beckage_taci_phase1_costs"] = f"${int(hourly_rate * phase1_hours):,}"
            pricing["beckage_taci_phase2_costs"] = f"${int(hourly_rate * phase2_hours):,}"
            pricing["beckage_taci_total_cost"] = f"${int(hourly_rate * (phase1_hours + phase2_hours)):,}"
        elif self.client and self.client.insurance_carrier == "Beazley":
            # Beazley-specific pricing - use high end of range
            phase1_hours = 15  # High end of range (was 5-15)
            phase2_hours = 25  # High end of range (was 5-25)

            # Single price model - no ranges

            # Use single price (high end of range)
            pricing["beazley_taci_phase1_costs"] = f"${int(hourly_rate * phase1_hours):,}"
            pricing["beazley_taci_phase2_costs"] = f"${int(hourly_rate * phase2_hours):,}"
            pricing["beazley_taci_total_cost"] = f"${int(hourly_rate * (phase1_hours + phase2_hours)):,}"
        else:
            # Standard pricing - convert to single price model
            phase1_hours = 15  # High end of range (was 5-15)
            phase2_hours = 25  # High end of range (was 5-25)

            # Single price model - no ranges

            # Use single price (high end of range)
            pricing["taci_phase1_costs"] = f"${int(hourly_rate * phase1_hours):,}"
            pricing["taci_phase2_costs"] = f"${int(hourly_rate * phase2_hours):,}"
            pricing["taci_total_cost"] = f"${int(hourly_rate * (phase1_hours + phase2_hours)):,}"

        return pricing

    def _calculate_data_download_pricing(self, pricing):
        """
        Calculate pricing for data download.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Get the hourly rate
        hourly_rate = pricing['taci_rate']

        # Use the formatted rate for display in documents
        pricing['taci_rate'] = pricing['taci_rate_formatted']

        # Always use single price model (high end of range if range was specified)
        hours = self.data_download_hours_max if self.data_download_hours_max > 0 else self.data_download_hours_min

        # Store the original range format for reference if it was a range
        if not self.is_single_price and self.data_download_hours_min != self.data_download_hours_max:
            pricing["data_download_hours_range"] = f"{self.data_download_hours_min} - {self.data_download_hours_max}"
            pricing["data_download_cost_range"] = f"${int(hourly_rate * self.data_download_hours_min):,} - ${int(hourly_rate * self.data_download_hours_max):,}"

        # Use single price
        pricing["data_download_hours"] = hours
        pricing["data_download_cost"] = f"${int(hourly_rate * hours):,}"

        return pricing

    def _calculate_ransom_site_monitoring_pricing(self, pricing):
        """
        Calculate pricing for ransom site monitoring.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Get the hourly rate
        hourly_rate = pricing['taci_rate']

        # Use the formatted rate for display in documents
        pricing['taci_rate'] = pricing['taci_rate_formatted']

        # Always use single price model (high end of range if range was specified)
        hours = self.ransom_site_monitoring_hours_max if self.ransom_site_monitoring_hours_max > 0 else self.ransom_site_monitoring_hours_min

        # Store the original range format for reference if it was a range
        if not self.is_single_price and self.ransom_site_monitoring_hours_min != self.ransom_site_monitoring_hours_max:
            pricing["ransom_site_monitoring_hours_range"] = f"{self.ransom_site_monitoring_hours_min} - {self.ransom_site_monitoring_hours_max}"
            pricing["ransom_site_monitoring_cost_range"] = f"${int(hourly_rate * self.ransom_site_monitoring_hours_min):,} - ${int(hourly_rate * self.ransom_site_monitoring_hours_max):,}"

        # Use single price
        pricing["ransom_site_monitoring_hours"] = hours
        pricing["ransom_site_monitoring_cost"] = f"${int(hourly_rate * hours):,}"

        return pricing

