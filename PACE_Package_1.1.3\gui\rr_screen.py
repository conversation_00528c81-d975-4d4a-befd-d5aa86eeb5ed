"""
Recovery and Remediation screen UI for PACE application.
"""

import sys
from PySide6.QtWidgets import (
    QW<PERSON>t, QLabel, QLineEdit, QComboBox, QCheckBox, QSpinBox,
    QPushButton, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QRadioButton, QButtonGroup, QApplication
)
from PySide6.QtCore import Qt, Signal

from models.client import Client
from models.rr import RREngagement

class RRScreen(QWidget):
    """
    Recovery and Remediation screen for the PACE application.
    """

    # Signal emitted when the user clicks the Generate Documents button
    generate_clicked = Signal(RREngagement)

    # Signal emitted when the user clicks the Back button
    back_clicked = Signal()

    def __init__(self, client):
        """
        Initialize the Recovery and Remediation screen.

        Args:
            client (Client): The client for this engagement
        """
        super().__init__()

        self.client = client
        self.engagement = RREngagement(client)

        self.init_ui()

    def init_ui(self):
        """
        Initialize the UI.
        """
        # Set up the main layout
        main_layout = QVBoxLayout()

        # Add the title
        title_label = QLabel("Recovery and Remediation")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Add the client information
        client_info_label = QLabel(f"Client: {self.client.name}")
        client_info_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(client_info_label)

        # Add a separator
        main_layout.addSpacing(20)

        # Create a form layout for the RR parameters
        form_layout = QFormLayout()

        # Add the assistance type options
        assistance_group = QGroupBox("Assistance Type")
        assistance_layout = QHBoxLayout()

        self.remote_radio = QRadioButton("Remote Assistance")
        self.onsite_radio = QRadioButton("Onsite Assistance")
        self.remote_radio.setChecked(True)

        # Connect radio buttons directly to slots
        self.remote_radio.clicked.connect(self.on_remote_clicked)
        self.onsite_radio.clicked.connect(self.on_onsite_clicked)

        # No button group - manage the radio buttons manually
        # This avoids potential issues with QButtonGroup

        assistance_layout.addWidget(self.remote_radio)
        assistance_layout.addWidget(self.onsite_radio)

        assistance_group.setLayout(assistance_layout)
        form_layout.addRow("", assistance_group)

        # Add the resource count field
        self.resource_count_label = QLabel("Number of resources needed:")
        self.resource_count_spin = QSpinBox()
        self.resource_count_spin.setRange(1, 10)
        self.resource_count_spin.setValue(1)

        # Add debug print statement for resource count
        self.resource_count_spin.valueChanged.connect(lambda value: print(f"Resource count changed: {value}"))
        form_layout.addRow(self.resource_count_label, self.resource_count_spin)

        # Add the decryption assistance checkbox
        self.decryption_check = QCheckBox("Include Decryption Assistance")
        form_layout.addRow("", self.decryption_check)

        # Add the MSA checkbox
        self.msa_check = QCheckBox("Generate Master Services Agreement")
        form_layout.addRow("", self.msa_check)

        # Add the form layout to the main layout
        main_layout.addLayout(form_layout)

        # Add a separator
        main_layout.addSpacing(20)

        # Add the buttons
        button_layout = QHBoxLayout()

        self.back_button = QPushButton("Back")
        self.back_button.clicked.connect(self.on_back_clicked)

        self.generate_button = QPushButton("Generate Documents")
        self.generate_button.clicked.connect(self.on_generate_clicked)

        button_layout.addWidget(self.back_button)
        button_layout.addStretch()
        button_layout.addWidget(self.generate_button)

        main_layout.addLayout(button_layout)

        # Set the main layout
        self.setLayout(main_layout)

        # Set the window properties
        self.setWindowTitle("PACE - Recovery and Remediation")
        self.resize(600, 300)

        # Initialize the UI state based on the default selection (Remote)
        self.resource_count_spin.setValue(1)
        self.resource_count_spin.setEnabled(False)
        self.resource_count_label.setEnabled(False)

    def on_back_clicked(self):
        """
        Handle the Back button click.
        """
        self.back_clicked.emit()

    def on_remote_clicked(self):
        """
        Handle the Remote Assistance radio button click.
        """
        print("Remote radio clicked")
        self.remote_radio.setChecked(True)
        self.onsite_radio.setChecked(False)

        # For remote assistance, disable the resource count field (always 1)
        self.resource_count_spin.setValue(1)
        self.resource_count_spin.setEnabled(False)
        self.resource_count_label.setEnabled(False)

    def on_onsite_clicked(self):
        """
        Handle the Onsite Assistance radio button click.
        """
        print("Onsite radio clicked")
        self.remote_radio.setChecked(False)
        self.onsite_radio.setChecked(True)

        # For onsite assistance, enable the resource count field
        self.resource_count_spin.setEnabled(True)
        self.resource_count_label.setEnabled(True)

    def on_generate_clicked(self):
        """
        Handle the Generate Documents button click.
        """
        # Update the engagement object with the form values
        self.engagement.is_remote = self.remote_radio.isChecked()

        # Set resource count based on remote/onsite selection
        if self.engagement.is_remote:
            self.engagement.resource_count = 1
            self.engagement.onsite_resources_count = 1
        else:
            self.engagement.resource_count = self.resource_count_spin.value()
            self.engagement.onsite_resources_count = self.resource_count_spin.value()

        self.engagement.include_decryption = self.decryption_check.isChecked()
        self.engagement.needs_msa = self.msa_check.isChecked()

        # Emit the generate_clicked signal
        self.generate_clicked.emit(self.engagement)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    client = Client("Test Client", "123 Main St", "AIG", "Baker & Hostetler LLP")
    rr_screen = RRScreen(client)
    rr_screen.show()
    sys.exit(app.exec())
