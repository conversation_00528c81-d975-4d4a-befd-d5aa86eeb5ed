"""
Utility module for replacing placeholders in Word documents using python-docx.
This is an alternative to docxtpl that might work better for some templates.
"""

import os
import re
import logging
import html
from docx import Document
from utils.path_resolver import resolve_template_path, get_all_template_files

logger = logging.getLogger(__name__)

def replace_placeholders_in_docx(template_path, output_path, placeholders):
    """
    Replace placeholders in a Word document using python-docx.

    Args:
        template_path (str): Path to the template file
        output_path (str): Path to save the generated document
        placeholders (dict): Dictionary of placeholders and values

    Returns:
        str: Path to the generated document, or None if generation failed
    """
    try:
        # Resolve the template path for bundled environments
        resolved_template_path = resolve_template_path(template_path)
        logger.info(f"Resolved template path: {resolved_template_path}")

        # Check if the template exists
        if not os.path.exists(resolved_template_path):
            logger.error(f"Template not found at resolved path: {resolved_template_path}")

            # List all available templates for debugging
            logger.info("Available templates:")
            for template_file in get_all_template_files():
                logger.info(f"  - {template_file}")

            return None

        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Load the document
        logger.info(f"Loading document: {resolved_template_path}")
        doc = Document(resolved_template_path)

        # Process placeholders to ensure all values are strings
        processed_placeholders = {}
        for key, value in placeholders.items():
            if value is None:
                processed_value = ""
            elif hasattr(value, 'text'):  # Handle RichText objects
                # For RichText objects, extract just the text without XML tags
                processed_value = value.text
                logger.info(f"Converted RichText object to plain text: {key} = {processed_value}")
            elif not isinstance(value, str):
                processed_value = str(value)
            else:
                processed_value = value

            # Clean up any XML tags that might be in the value
            if isinstance(processed_value, str):
                # Remove XML tags using regex
                processed_value = re.sub(r'<[^>]+>', '', processed_value)
                # Replace XML entities
                processed_value = processed_value.replace('&amp;', '&')
                # Decode HTML entities
                processed_value = html.unescape(processed_value)
                # Handle specific HTML entities that might not be caught
                entity_map = {
                    '&#x27;': "'",  # Apostrophe
                    '&apos;': "'",  # Apostrophe
                    '&quot;': '"',  # Double quote
                }
                for entity, char in entity_map.items():
                    processed_value = processed_value.replace(entity, char)

            processed_placeholders[key] = processed_value

        # Replace placeholders in paragraphs
        for paragraph in doc.paragraphs:
            replace_text_in_paragraph(paragraph, processed_placeholders)

        # Replace placeholders in tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        replace_text_in_paragraph(paragraph, processed_placeholders)

        # Save the document
        logger.info(f"Saving document to: {output_path}")
        try:
            # Try to save the document
            doc.save(output_path)
        except PermissionError:
            # If the file is already open, save to a temporary file and then rename it
            logger.warning(f"Permission denied when saving to {output_path}, trying with a temporary file")
            temp_path = f"{output_path}.temp"
            doc.save(temp_path)

            # Try to remove the original file if it exists
            try:
                if os.path.exists(output_path):
                    os.remove(output_path)
            except Exception as e:
                logger.error(f"Error removing original file: {e}")
                return None

            # Rename the temporary file to the original file name
            try:
                os.rename(temp_path, output_path)
            except Exception as e:
                logger.error(f"Error renaming temporary file: {e}")
                return None

        # Verify the document was created
        if os.path.exists(output_path):
            logger.info(f"Document generated successfully: {output_path}")
            return output_path
        else:
            logger.error(f"Document generation failed: Output file not found at {output_path}")
            return None
    except Exception as e:
        logger.error(f"Error replacing placeholders in document: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def replace_text_in_paragraph(paragraph, placeholders):
    """
    Replace placeholders in a paragraph.

    Args:
        paragraph: The paragraph to process
        placeholders (dict): Dictionary of placeholders and values
    """
    if not paragraph.text:
        return

    # First, check if the paragraph contains any placeholders
    contains_placeholder = False
    for key in placeholders.keys():
        pattern = r'\{\{' + re.escape(key) + r'\}\}'
        if re.search(pattern, paragraph.text):
            contains_placeholder = True
            break

    # If no placeholders found, return early
    if not contains_placeholder:
        return

    # Get the full paragraph text
    full_text = paragraph.text

    # Replace all placeholders in the full text
    for key, value in placeholders.items():
        pattern = r'\{\{' + re.escape(key) + r'\}\}'
        full_text = re.sub(pattern, str(value), full_text)

    # Clear the paragraph and add the new text
    for run in paragraph.runs:
        run.text = ""

    # Add the new text to the first run
    if paragraph.runs:
        paragraph.runs[0].text = full_text
    else:
        # If there are no runs, create a new one
        paragraph.add_run(full_text)
