"""
Business Email Compromise (BEC) engagement model for PACE application.
"""

from models.engagement import Engagement
from data.carriers import get_carrier_rate
from data.pricing_tables import (
    get_bec_m365_price, get_bec_gcp_price,
    get_coalition_bec_m365_price, get_coalition_bec_gcp_price,
    get_beazley_bec_m365_price
)

class BECEngagement(Engagement):
    """
    Class representing a Business Email Compromise (BEC) engagement in the PACE application.
    """

    def __init__(self, client=None):
        """
        Initialize a new BECEngagement object.

        Args:
            client (Client): The client for this engagement
        """
        super().__init__(client, "BEC")
        self.email_platform = "M365"  # M365, Google Cloud Platform, Exchange
        self.tenant_size = 0
        self.mailbox_count = 0
        self.include_message_extraction = False
        self.message_extraction_cost = 0
        self.include_triage_analysis = False
        self.triage_count = 0
        self.is_e5_license = False

        # Advanced options
        self.is_hybrid_environment = False
        self.export_pst = False
        self.export_eml = False
        self.export_msg = False
        self.include_timeline_reconstruction = False
        self.include_forensic_analysis = False
        self.include_threat_attribution = False
        self.timeframe = "standard"  # standard, expedited, or emergency
        self.price_adjustment = 0

    def to_dict(self):
        """
        Convert the BEC engagement data to a dictionary.

        Returns:
            dict: A dictionary containing the BEC engagement data
        """
        data = super().to_dict()
        data.update({
            "email_platform": self.email_platform,
            "tenant_size": self.tenant_size,
            "mailbox_count": self.mailbox_count,
            "include_message_extraction": self.include_message_extraction,
            "message_extraction_cost": self.message_extraction_cost,
            "include_triage_analysis": self.include_triage_analysis,
            "triage_count": self.triage_count,
            "is_e5_license": self.is_e5_license,

            # Advanced options
            "is_hybrid_environment": self.is_hybrid_environment,
            "export_pst": self.export_pst,
            "export_eml": self.export_eml,
            "export_msg": self.export_msg,
            "include_timeline_reconstruction": self.include_timeline_reconstruction,
            "include_forensic_analysis": self.include_forensic_analysis,
            "include_threat_attribution": self.include_threat_attribution,
            "timeframe": self.timeframe,
            "price_adjustment": self.price_adjustment
        })
        return data

    @classmethod
    def from_dict(cls, data, client=None):
        """
        Create a BECEngagement object from a dictionary.

        Args:
            data (dict): A dictionary containing BEC engagement data
            client (Client): The client for this engagement

        Returns:
            BECEngagement: A new BECEngagement object
        """
        engagement = super().from_dict(data, client)

        engagement.email_platform = data.get("email_platform", "M365")
        engagement.tenant_size = data.get("tenant_size", 0)
        engagement.mailbox_count = data.get("mailbox_count", 0)
        engagement.include_message_extraction = data.get("include_message_extraction", False)
        engagement.message_extraction_cost = data.get("message_extraction_cost", 0)
        engagement.include_triage_analysis = data.get("include_triage_analysis", False)
        engagement.triage_count = data.get("triage_count", 0)
        engagement.is_e5_license = data.get("is_e5_license", False)

        # Advanced options
        engagement.is_hybrid_environment = data.get("is_hybrid_environment", False)
        engagement.export_pst = data.get("export_pst", False)
        engagement.export_eml = data.get("export_eml", False)
        engagement.export_msg = data.get("export_msg", False)
        engagement.include_timeline_reconstruction = data.get("include_timeline_reconstruction", False)
        engagement.include_forensic_analysis = data.get("include_forensic_analysis", False)
        engagement.include_threat_attribution = data.get("include_threat_attribution", False)
        engagement.timeframe = data.get("timeframe", "standard")
        engagement.price_adjustment = data.get("price_adjustment", 0)

        return engagement

    def is_valid(self):
        """
        Check if the BEC engagement data is valid.

        Returns:
            bool: True if the BEC engagement data is valid, False otherwise
        """
        if not super().is_valid():
            return False

        # Email platform must be one of the supported platforms
        if self.email_platform not in ["M365", "Google Cloud Platform", "Exchange"]:
            return False

        # Tenant size must be specified
        if self.tenant_size <= 0:
            return False

        # Mailbox count must be specified
        if self.mailbox_count <= 0:
            return False

        # If message extraction is included, cost must be specified
        if self.include_message_extraction and self.message_extraction_cost <= 0:
            return False

        # If triage analysis is included, count must be specified
        if self.include_triage_analysis and self.triage_count <= 0:
            return False

        return True

    def get_document_folder_name(self):
        """
        Get the name of the folder where documents for this engagement should be saved.

        Returns:
            str: The folder name
        """
        import re

        if not self.client or not self.client.name:
            return "Unnamed Client - Scoping Documents"

        # Sanitize client name for folder name
        # Replace special characters with underscores
        safe_name = re.sub(r'[\\/*?:"<>|]', '_', self.client.name)
        # Limit length to avoid path length issues
        if len(safe_name) > 50:
            safe_name = safe_name[:47] + '...'

        return f"{safe_name} - Scoping Documents"

    def get_sow_filename(self):
        """
        Get the filename for the SOW document.

        Returns:
            str: The filename for the SOW document
        """
        from datetime import datetime
        import re

        if not self.client or not self.client.name:
            return f"Unnamed Client BEC Investigation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

        # Sanitize client name for filename
        # Replace special characters with underscores
        safe_name = re.sub(r'[\\/*?:"<>|]', '_', self.client.name)
        # Limit length to avoid path length issues
        if len(safe_name) > 50:
            safe_name = safe_name[:47] + '...'

        return f"{safe_name} BEC Investigation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

    def get_msa_filename(self):
        """
        Get the filename for the MSA document.

        Returns:
            str: The filename for the MSA document
        """
        from datetime import datetime
        import re

        if not self.client or not self.client.name:
            return f"Unnamed Client Master Services Agreement (MSA) {datetime.now().strftime('%Y%m%d')}.docx"

        # Sanitize client name for filename
        # Replace special characters with underscores
        safe_name = re.sub(r'[\\/*?:"<>|]', '_', self.client.name)
        # Limit length to avoid path length issues
        if len(safe_name) > 50:
            safe_name = safe_name[:47] + '...'

        return f"{safe_name} Master Services Agreement (MSA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_baa_filename(self):
        """
        Get the filename for the BAA document.

        Returns:
            str: The filename for the BAA document
        """
        from datetime import datetime
        import re

        if not self.client or not self.client.name:
            return f"Unnamed Client Business Associate Agreement (BAA) {datetime.now().strftime('%Y%m%d')}.docx"

        # Sanitize client name for filename
        # Replace special characters with underscores
        safe_name = re.sub(r'[\\/*?:"<>|]', '_', self.client.name)
        # Limit length to avoid path length issues
        if len(safe_name) > 50:
            safe_name = safe_name[:47] + '...'

        return f"{safe_name} Business Associate Agreement (BAA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_dpa_filename(self):
        """
        Get the filename for the DPA document.

        Returns:
            str: The filename for the DPA document
        """
        from datetime import datetime
        import re

        if not self.client or not self.client.name:
            return f"Unnamed Client Data Processing Agreement (DPA) {datetime.now().strftime('%Y%m%d')}.docx"

        # Sanitize client name for filename
        # Replace special characters with underscores
        safe_name = re.sub(r'[\\/*?:"<>|]', '_', self.client.name)
        # Limit length to avoid path length issues
        if len(safe_name) > 50:
            safe_name = safe_name[:47] + '...'

        return f"{safe_name} Data Processing Agreement (DPA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_sow_template_path(self):
        """
        Get the path to the SOW template for this engagement.

        Returns:
            str: The path to the SOW template
        """
        # Base path for BEC templates
        base_path = "templates/base_templates/bec"

        # Special case templates based on law firm and carrier
        if self.client:
            if self.client.law_firm == "Baker & Hostetler LLP":
                if self.email_platform == "M365":
                    return f"{base_path}/SOW_Baker_M365_BEC_template.docx"
                elif self.email_platform == "Google Cloud Platform":
                    return f"{base_path}/SOW_Baker_GCP_BEC_template.docx"

            if self.client.law_firm == "Dykema Gossett PLLC":
                if self.email_platform == "M365":
                    return f"{base_path}/SOW_Dykema_M365_BEC_template.docx"
                elif self.email_platform == "Google Cloud Platform":
                    return f"{base_path}/SOW_Dykema_GCP_BEC_template.docx"

            if self.client.law_firm == "Woods Rogers PLC":
                if self.email_platform == "M365":
                    return f"{base_path}/SOW_Woods_M365_BEC_template.docx"

            if self.client.insurance_carrier == "Beazley":
                if self.email_platform == "M365":
                    # Beazley doesn't have a template in the base_templates directory
                    # Use the standard M365 template instead
                    return f"{base_path}/SOW_M365_BEC_template.docx"

            if self.client.insurance_carrier == "Chubb":
                if self.email_platform == "M365":
                    return f"{base_path}/SOW_Chubb_M365_BEC_template.docx"
                elif self.email_platform == "Google Cloud Platform":
                    return f"{base_path}/SOW_Chubb_GCP_BEC_template.docx"
                elif self.email_platform == "Exchange":
                    return f"{base_path}/SOW_Chubb_Exchange_BEC_template.docx"

            if self.client.insurance_carrier == "Coalition":
                if self.email_platform == "M365":
                    return f"{base_path}/SOW_Coalition_M365_BEC_template.docx"
                elif self.email_platform == "Google Cloud Platform":
                    return f"{base_path}/SOW_Coalition_GCP_BEC_template.docx"

        # Default templates based on email platform
        if self.email_platform == "M365":
            return f"{base_path}/SOW_M365_BEC_template.docx"
        elif self.email_platform == "Google Cloud Platform":
            return f"{base_path}/SOW_GCP_BEC_template.docx"
        elif self.email_platform == "Exchange":
            return f"{base_path}/SOW_Exchange_BEC_template.docx"

        # Fallback template
        return f"{base_path}/SOW_M365_BEC_template.docx"

    def get_msa_template_path(self):
        """
        Get the path to the MSA template for this engagement.

        Returns:
            str: The path to the MSA template
        """
        if self.client and self.client.law_firm:
            # Check for law firm-specific MSA templates
            # Special case for Baker & Hostetler LLP with TASB
            if self.client.law_firm == "Baker & Hostetler LLP" and hasattr(self.client, 'is_tasb') and self.client.is_tasb:
                return "templates/base_templates/msa_templates/Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx"
            # Special case for Jackson Lewis PC with BAA
            elif self.client.law_firm == "Jackson Lewis PC" and self.needs_baa:
                return "templates/base_templates/msa_templates/Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx"
            # Standard law firm templates
            elif self.client.law_firm == "Baker & Hostetler LLP":
                return "templates/base_templates/msa_templates/Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Baker, Donelson, Bearman, Caldwell & Berkowitz P.C":
                return "templates/base_templates/msa_templates/Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Cipriani & Werner PC":
                return "templates/base_templates/msa_templates/Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Clark Hill PLC":
                return "templates/base_templates/msa_templates/Clark_Hill_PLC_Master_Services_Agreement.docx"
            elif self.client.law_firm == "Constangy, Brooks, Smith & Prophete, LLP":
                return "templates/base_templates/msa_templates/Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Dykema Gossett PLLC":
                return "templates/base_templates/msa_templates/Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx"
            elif self.client.law_firm == "Eckert Seamans Cherin & Mellott, LLC":
                return "templates/base_templates/msa_templates/Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx"
            elif self.client.law_firm == "Gordon Rees Scully Mansukhani, LLP":
                return "templates/base_templates/msa_templates/Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Greenberg Traurig, LLP":
                return "templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Jackson Lewis PC":
                return "templates/base_templates/msa_templates/Jackson_Lewis_PC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Lewis Brisbois Bisgaard & Smith, LLP":
                return "templates/base_templates/msa_templates/Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Locke Lord":
                return "templates/base_templates/msa_templates/Locke_Lord_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Maynard Nexsen PC":
                return "templates/base_templates/msa_templates/Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "McDonald Hopkins LLC":
                return "templates/base_templates/msa_templates/McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Mullen Coughlin LLC":
                return "templates/base_templates/msa_templates/Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Nelson Mullins Riley & Scarborough LLP":
                return "templates/base_templates/msa_templates/Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Norton Rose Fulbright":
                return "templates/base_templates/msa_templates/Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Octillo Law PLLC":
                return "templates/base_templates/msa_templates/Octillo_Law_PLLC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Ogletree Deakins":
                return "templates/base_templates/msa_templates/Ogletree_Deakins_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Ruskin Moscou Faltischek, P.C.":
                return "templates/base_templates/msa_templates/Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Shook, Hardy & Bacon L.L.P.":
                return "templates/base_templates/msa_templates/Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "The Beckage Firm PLLC" or self.client.law_firm == "The Beckage Firm":
                return "templates/base_templates/msa_templates/The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Woods Rogers":
                return "templates/base_templates/msa_templates/Woods_Rogers_Master_Services_Agreement_Template.docx"
            else:
                # Generic 3-party MSA for any other law firm
                return "templates/base_templates/msa_templates/IR_Services_3_Party_Master_Services_Agreement_Template.docx"

        # Default 2-party MSA template (no law firm)
        return "templates/base_templates/msa_templates/IR_Services_2_Party_Master_Services_Agreement_Template.docx"

    def get_baa_template_path(self):
        """
        Get the path to the BAA template for this engagement.

        Returns:
            str: The path to the BAA template
        """
        # Use the standard BAA template with spaces (same format as DPA)
        return "templates/base_templates/baa/Template_Business_Associate_Agreement.docx"

    def get_dpa_template_path(self):
        """
        Get the path to the DPA template for this engagement.

        Returns:
            str: The path to the DPA template
        """
        # Use the original DPA template with spaces (same format as BAA)
        return "templates/base_templates/dpa/Template_Data_Processing_Agreement.docx"

    def get_mailbox_range(self):
        """
        Get the mailbox range for pricing calculations.

        Returns:
            str: The mailbox range ("1-5", "1-10", "1-15", or "1-20")
        """
        if self.mailbox_count <= 5:
            return "1-5"
        elif self.mailbox_count <= 10:
            return "1-10"
        elif self.mailbox_count <= 15:
            return "1-15"
        elif self.mailbox_count <= 20:
            return "1-20"
        else:
            return "1-15"  # Default to 1-15 for larger mailbox counts

    def calculate_pricing(self):
        """
        Calculate pricing for this BEC engagement.

        Returns:
            dict: A dictionary containing pricing information
        """
        pricing = {}

        # Add the BEC hourly rate
        if self.client and self.client.insurance_carrier:
            pricing["bec_rate"] = get_carrier_rate(self.client.insurance_carrier, "BEC")
        else:
            pricing["bec_rate"] = 450.0  # Default rate

        # Get the mailbox range for pricing calculations
        mailbox_range = self.get_mailbox_range()

        # Calculate pricing based on email platform and special cases
        if self.email_platform == "M365":
            if self.client and self.client.law_firm == "Baker & Hostetler LLP":
                return self._calculate_baker_m365_pricing(pricing, mailbox_range)
            elif self.client and self.client.law_firm == "Dykema Gossett PLLC":
                return self._calculate_dykema_m365_pricing(pricing, mailbox_range)
            elif self.client and self.client.law_firm == "Woods Rogers PLC":
                return self._calculate_woods_m365_pricing(pricing, mailbox_range)
            elif self.client and self.client.insurance_carrier == "Beazley":
                return self._calculate_beazley_m365_pricing(pricing)
            elif self.client and self.client.insurance_carrier == "Chubb":
                return self._calculate_chubb_m365_pricing(pricing, mailbox_range)
            elif self.client and self.client.insurance_carrier == "Coalition":
                return self._calculate_coalition_m365_pricing(pricing, mailbox_range)
            else:
                return self._calculate_standard_m365_pricing(pricing, mailbox_range)
        elif self.email_platform == "Google Cloud Platform":
            if self.client and self.client.law_firm == "Baker & Hostetler LLP":
                return self._calculate_baker_gcp_pricing(pricing, mailbox_range)
            elif self.client and self.client.law_firm == "Dykema Gossett PLLC":
                return self._calculate_dykema_gcp_pricing(pricing, mailbox_range)
            elif self.client and self.client.insurance_carrier == "Chubb":
                return self._calculate_chubb_gcp_pricing(pricing, mailbox_range)
            elif self.client and self.client.insurance_carrier == "Coalition":
                return self._calculate_coalition_gcp_pricing(pricing, mailbox_range)
            else:
                return self._calculate_standard_gcp_pricing(pricing, mailbox_range)
        elif self.email_platform == "Exchange":
            if self.client and self.client.insurance_carrier == "Chubb":
                return self._calculate_chubb_exchange_pricing(pricing)
            else:
                return self._calculate_standard_exchange_pricing(pricing)

        return pricing

    def _calculate_standard_m365_pricing(self, pricing, mailbox_range):
        """
        Calculate standard M365 pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["fixed_cost_rate"] = get_bec_m365_price(mailbox_range, self.tenant_size, self.is_e5_license)
        pricing["num_users"] = self.mailbox_count
        pricing["tenant_analysis"] = pricing["fixed_cost_rate"]
        pricing["triage_num"] = self.triage_count

        # Message extraction
        if self.include_message_extraction:
            pricing["m365_message_extraction"] = self.message_extraction_cost

        # Calculate total cost
        total_cost = pricing["fixed_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        if self.include_message_extraction:
            total_cost += self.message_extraction_cost

        pricing["m365_total_cost"] = total_cost

        return pricing

    def _calculate_baker_m365_pricing(self, pricing, mailbox_range):
        """
        Calculate Baker & Hostetler LLP M365 pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["baker_m365_fixed_cost_rate"] = get_bec_m365_price(mailbox_range, self.tenant_size, self.is_e5_license)
        pricing["baker_m365_num_users"] = self.mailbox_count
        pricing["baker_m365_num_mailboxes"] = self.mailbox_count
        pricing["baker_m365_tenant_analysis"] = pricing["baker_m365_fixed_cost_rate"]
        pricing["baker_m365_triage_num"] = self.triage_count

        # Message extraction
        if self.include_message_extraction:
            pricing["baker_m365_message_extraction"] = self.message_extraction_cost

        # Calculate total cost
        total_cost = pricing["baker_m365_fixed_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        if self.include_message_extraction:
            total_cost += self.message_extraction_cost

        pricing["baker_m365_total_cost"] = total_cost

        return pricing

    def _calculate_dykema_m365_pricing(self, pricing, mailbox_range):
        """
        Calculate Dykema Gossett PLLC M365 pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["dykema_m365_fixed_cost_rate"] = get_bec_m365_price(mailbox_range, self.tenant_size, self.is_e5_license)
        pricing["dykema_m365_num_users"] = self.mailbox_count
        pricing["dykema_m365_num_mailboxes"] = self.mailbox_count
        pricing["dykema_m365_tenant_analysis"] = pricing["dykema_m365_fixed_cost_rate"]
        pricing["dykema_m365_triage_num"] = self.triage_count

        # Message extraction
        if self.include_message_extraction:
            pricing["dykema_m365_message_extraction"] = self.message_extraction_cost

        # Calculate total cost
        total_cost = pricing["dykema_m365_fixed_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        if self.include_message_extraction:
            total_cost += self.message_extraction_cost

        pricing["dykema_m365_total_cost"] = total_cost

        return pricing

    def _calculate_woods_m365_pricing(self, pricing, mailbox_range):
        """
        Calculate Woods Rogers PLC M365 pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["woods_m365_fixed_cost_rate"] = get_bec_m365_price(mailbox_range, self.tenant_size, self.is_e5_license)
        pricing["woods_m365_num_users"] = self.mailbox_count
        pricing["woods_m365_num_mailboxes"] = self.mailbox_count
        pricing["woods_m365_tenant_analysis"] = pricing["woods_m365_fixed_cost_rate"]
        pricing["woods_triage_num"] = self.triage_count

        # Message extraction
        if self.include_message_extraction:
            pricing["woods_m365_message_extraction"] = self.message_extraction_cost

        # Calculate total cost
        total_cost = pricing["woods_m365_fixed_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        if self.include_message_extraction:
            total_cost += self.message_extraction_cost

        pricing["woods_m365_total_cost"] = total_cost

        return pricing

    def _calculate_beazley_m365_pricing(self, pricing):
        """
        Calculate Beazley M365 pricing.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["beazley_m365_fixed_cost_rate"] = get_beazley_bec_m365_price(self.mailbox_count)
        pricing["beazley_m365_num_users"] = self.mailbox_count

        # Set mailbox range display
        if self.mailbox_count <= 5:
            pricing["beazley_m365_num_mailboxes"] = "1 - 5"
        elif self.mailbox_count <= 10:
            pricing["beazley_m365_num_mailboxes"] = "5 - 10"
        elif self.mailbox_count <= 15:
            pricing["beazley_m365_num_mailboxes"] = "10 - 15"

        pricing["beazley_m365_tenant_analysis"] = pricing["beazley_m365_fixed_cost_rate"]

        # Message extraction
        if self.include_message_extraction:
            pricing["beazley_m365_message_extraction"] = self.message_extraction_cost
            pricing["beazley_m365_total_cost"] = pricing["beazley_m365_fixed_cost_rate"] + self.message_extraction_cost
        else:
            pricing["beazley_m365_total_cost"] = pricing["beazley_m365_fixed_cost_rate"]

        return pricing

    def _calculate_chubb_m365_pricing(self, pricing, mailbox_range):
        """
        Calculate Chubb M365 pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["chubb_m365_fixed_cost_rate"] = get_bec_m365_price(mailbox_range, self.tenant_size, self.is_e5_license)
        pricing["chubb_m365_num_users"] = self.mailbox_count

        # Set mailbox range display
        if self.mailbox_count <= 5:
            pricing["chubb_m365_num_mailboxes"] = "1 - 5"
        elif self.mailbox_count <= 10:
            pricing["chubb_m365_num_mailboxes"] = "5 - 10"
        elif self.mailbox_count <= 15:
            pricing["chubb_m365_num_mailboxes"] = "10 - 15"

        pricing["chubb_m365_tenant_analysis"] = pricing["chubb_m365_fixed_cost_rate"]
        pricing["chubb_m365_triage_num"] = self.triage_count

        # Add the mandatory $599 fee for Chubb
        pricing["chubb_m365_mandatory_fee"] = 599

        # Message extraction
        if self.include_message_extraction:
            pricing["chubb_m365_message_extraction"] = self.message_extraction_cost

        # Calculate total cost
        total_cost = pricing["chubb_m365_fixed_cost_rate"]

        # Add the mandatory $599 fee to the total cost
        total_cost += 599

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        if self.include_message_extraction:
            total_cost += self.message_extraction_cost

        pricing["chubb_m365_total_cost"] = total_cost

        return pricing

    def _calculate_coalition_m365_pricing(self, pricing, mailbox_range):
        """
        Calculate Coalition M365 pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["coalition_m365_fixed_cost_rate"] = get_coalition_bec_m365_price(mailbox_range, self.tenant_size, self.is_e5_license)
        pricing["coalition_m365_num_users"] = self.mailbox_count

        # Set mailbox range display
        if self.mailbox_count <= 5:
            pricing["coalition_m365_num_mailboxes"] = "1 - 5"
        elif self.mailbox_count <= 10:
            pricing["coalition_m365_num_mailboxes"] = "5 - 10"
        elif self.mailbox_count <= 15:
            pricing["coalition_m365_num_mailboxes"] = "10 - 15"

        pricing["coalition_m365_tenant_analysis"] = pricing["coalition_m365_fixed_cost_rate"]
        pricing["coalition_m365_triage_num"] = self.triage_count

        # Message extraction
        if self.include_message_extraction:
            pricing["coalition_m365_message_extraction"] = self.message_extraction_cost

        # Calculate total cost
        total_cost = pricing["coalition_m365_fixed_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 750  # Executive summary (Coalition uses $750 instead of $1000)

        if self.include_message_extraction:
            total_cost += self.message_extraction_cost

        pricing["coalition_m365_total_cost"] = total_cost

        return pricing

    def _calculate_standard_gcp_pricing(self, pricing, mailbox_range):
        """
        Calculate standard Google Cloud Platform pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["fixed_gcp_cost_rate"] = get_bec_gcp_price(mailbox_range, self.tenant_size)
        pricing["num_gcp_users"] = self.mailbox_count
        pricing["gcp_tenant_analysis"] = pricing["fixed_gcp_cost_rate"]
        pricing["gcp_triage_num"] = self.triage_count

        # Calculate total cost
        total_cost = pricing["fixed_gcp_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        pricing["gcp_total_cost"] = total_cost

        return pricing

    def _calculate_baker_gcp_pricing(self, pricing, mailbox_range):
        """
        Calculate Baker & Hostetler LLP Google Cloud Platform pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["baker_gcp_fixed_cost_rate"] = get_bec_gcp_price(mailbox_range, self.tenant_size)
        pricing["baker_gcp_num_users"] = self.mailbox_count
        pricing["baker_gcp_tenant_analysis"] = pricing["baker_gcp_fixed_cost_rate"]
        pricing["baker_gcp_triage_num"] = self.triage_count

        # Calculate total cost
        total_cost = pricing["baker_gcp_fixed_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        pricing["baker_gcp_total_cost"] = total_cost

        return pricing

    def _calculate_dykema_gcp_pricing(self, pricing, mailbox_range):
        """
        Calculate Dykema Gossett PLLC Google Cloud Platform pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["dykema_gcp_fixed_cost_rate"] = get_bec_gcp_price(mailbox_range, self.tenant_size)
        pricing["dykema_gcp_num_users"] = self.mailbox_count
        pricing["dykema_gcp_tenant_analysis"] = pricing["dykema_gcp_fixed_cost_rate"]
        pricing["dykema_gcp_triage_num"] = self.triage_count

        # Calculate total cost
        total_cost = pricing["dykema_gcp_fixed_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        pricing["dykema_gcp_total_cost"] = total_cost

        return pricing

    def _calculate_chubb_gcp_pricing(self, pricing, mailbox_range):
        """
        Calculate Chubb Google Cloud Platform pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["chubb_gcp_fixed_cost_rate"] = get_bec_gcp_price(mailbox_range, self.tenant_size)
        pricing["chubb_gcp_num_users"] = self.mailbox_count

        # Set mailbox range display
        if self.mailbox_count <= 5:
            pricing["chubb_gcp_num_mailboxes"] = "1 - 5"
        elif self.mailbox_count <= 10:
            pricing["chubb_gcp_num_mailboxes"] = "5 - 10"
        elif self.mailbox_count <= 15:
            pricing["chubb_gcp_num_mailboxes"] = "10 - 15"

        pricing["chubb_gcp_tenant_analysis"] = pricing["chubb_gcp_fixed_cost_rate"]
        pricing["chubb_gcp_triage_num"] = self.triage_count

        # Add the mandatory $599 fee for Chubb
        pricing["chubb_gcp_mandatory_fee"] = 599

        # Calculate total cost
        total_cost = pricing["chubb_gcp_fixed_cost_rate"]

        # Add the mandatory $599 fee to the total cost
        total_cost += 599

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 1000  # Executive summary

        pricing["chubb_gcp_total_cost"] = total_cost

        return pricing

    def _calculate_coalition_gcp_pricing(self, pricing, mailbox_range):
        """
        Calculate Coalition Google Cloud Platform pricing.

        Args:
            pricing (dict): The pricing dictionary to update
            mailbox_range (str): The mailbox range

        Returns:
            dict: The updated pricing dictionary
        """
        # Basic information
        pricing["coalition_gcp_fixed_cost_rate"] = get_coalition_bec_gcp_price(mailbox_range, self.tenant_size)
        pricing["coalition_gcp_num_users"] = self.mailbox_count
        pricing["coalition_gcp_tenant_analysis"] = pricing["coalition_gcp_fixed_cost_rate"]
        pricing["coalition_gcp_triage_num"] = self.triage_count

        # Calculate total cost
        total_cost = pricing["coalition_gcp_fixed_cost_rate"]

        if self.include_triage_analysis:
            triage_cost = 1500 * self.triage_count
            total_cost += triage_cost
            total_cost += 750  # Executive summary (Coalition uses $750 instead of $1000)

        pricing["coalition_gcp_total_cost"] = total_cost

        return pricing

    def _calculate_standard_exchange_pricing(self, pricing):
        """
        Calculate standard Exchange pricing.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Exchange pricing is typically custom, so we'll just set some placeholder values
        pricing["exchange_custom_pricing"] = True

        return pricing

    def _calculate_chubb_exchange_pricing(self, pricing):
        """
        Calculate Chubb Exchange pricing.

        Args:
            pricing (dict): The pricing dictionary to update

        Returns:
            dict: The updated pricing dictionary
        """
        # Chubb Exchange pricing is typically custom, so we'll just set some placeholder values
        pricing["chubb_exchange_custom_pricing"] = True

        # Add the mandatory $599 fee for Chubb
        pricing["chubb_exchange_mandatory_fee"] = 599

        # If there's a total cost field for Exchange, add the fee to it
        if "chubb_exchange_total_cost" in pricing:
            pricing["chubb_exchange_total_cost"] += 599
        else:
            # Set a default total cost with the mandatory fee
            pricing["chubb_exchange_total_cost"] = 599

        return pricing

    def get_placeholders(self):
        """
        Get the placeholders for document generation.

        Returns:
            dict: A dictionary of placeholders and their values
        """
        from datetime import datetime

        # Calculate pricing based on the engagement type and client
        try:
            pricing = self.calculate_pricing()
            if pricing is None:
                print("Warning: Pricing calculation returned None, using default pricing")
                pricing = {}
        except Exception as e:
            print(f"Error calculating pricing: {e}")
            import traceback
            traceback.print_exc()
            pricing = {}

        # Import RichText for handling ampersands
        try:
            from docxtpl import RichText
            # Create RichText objects for fields that might contain ampersands
            client_name_rt = RichText(self.client.name) if self.client and self.client.name else RichText("")
            client_address_rt = RichText(self.client.address) if self.client and self.client.address else RichText("")
            law_firm_rt = RichText(self.client.law_firm) if self.client and self.client.law_firm else RichText("")

            # Print debug information
            if self.client and self.client.law_firm and '&' in self.client.law_firm:
                print(f"Law firm name contains ampersand: {self.client.law_firm}")
                print(f"Using RichText for law firm name to preserve ampersand")
        except ImportError:
            # If RichText is not available, use regular strings
            print("Warning: RichText not available, ampersands may not be preserved")
            client_name_rt = self.client.name if self.client else ""
            client_address_rt = self.client.address if self.client and self.client.address else ""
            law_firm_rt = self.client.law_firm if self.client and self.client.law_firm else ""

        # Basic placeholders
        placeholders = {
            # Client information
            "client": client_name_rt,
            "Client": client_name_rt,  # Capitalized version
            "client_name": client_name_rt,  # Alternative name
            "company": client_name_rt,  # Alternative name
            "client_address": client_address_rt,
            "address": client_address_rt,  # Alternative name
            "date": datetime.now().strftime("%Y%m%d"),
            "formatted_date": datetime.now().strftime("%B %d, %Y"),
            "current_date": datetime.now().strftime("%B %d, %Y"),  # Alternative name
            "law_firm": law_firm_rt,
            "Lawfirm": law_firm_rt,  # Capitalized version
            "lawfirm": law_firm_rt,  # Lowercase version for MSA template
            "firm": law_firm_rt,  # Alternative name
            "counsel": law_firm_rt,  # Alternative name
            "CLIENT": client_name_rt,  # For specific law firm MSA templates
            "CLIENT_NAME": client_name_rt,  # For Baker Donelson MSA template

            # BEC rate
            "bec_rate": pricing.get("bec_rate", 450),

            # Email platform
            "email_platform": self.email_platform,

            # Tenant size and mailbox count
            "tenant_size": f"{self.tenant_size:,}",
            "num_users": f"{self.tenant_size:,}",  # Alternative name for tenant size
            "mailbox_count": f"{self.mailbox_count:,}",

            # E5 license
            "is_e5_license": "Yes" if self.is_e5_license else "No",

            # Message extraction
            "include_message_extraction": "Yes" if self.include_message_extraction else "No",
            "message_extraction_cost": f"${self.message_extraction_cost:,}" if self.include_message_extraction else "$0",

            # Triage analysis
            "include_triage_analysis": "Yes" if self.include_triage_analysis else "No",
            "triage_count": f"{self.triage_count}" if self.include_triage_analysis else "0",
            "triage_num": f"{self.triage_count}" if self.include_triage_analysis else "0",  # Alternative name for triage count

            # Advanced options
            "is_hybrid_environment": "Yes" if self.is_hybrid_environment else "No",
            "hybrid_environment": "Yes" if self.is_hybrid_environment else "No",  # Alternative name
            "export_pst": "Yes" if self.export_pst else "No",
            "export_eml": "Yes" if self.export_eml else "No",
            "export_msg": "Yes" if self.export_msg else "No",
            "include_timeline_reconstruction": "Yes" if self.include_timeline_reconstruction else "No",
            "timeline_reconstruction": "Yes" if self.include_timeline_reconstruction else "No",  # Alternative name
            "include_forensic_analysis": "Yes" if self.include_forensic_analysis else "No",
            "forensic_analysis": "Yes" if self.include_forensic_analysis else "No",  # Alternative name
            "include_threat_attribution": "Yes" if self.include_threat_attribution else "No",
            "threat_attribution": "Yes" if self.include_threat_attribution else "No",  # Alternative name
            "timeframe": self.timeframe.capitalize(),
            "investigation_timeframe": self.timeframe.capitalize(),  # Alternative name
            "price_adjustment": f"${self.price_adjustment:,}" if self.price_adjustment != 0 else "$0",
            "custom_price_adjustment": f"${self.price_adjustment:,}" if self.price_adjustment != 0 else "$0"  # Alternative name
        }

        # Add pricing placeholders based on email platform and law firm/carrier
        if self.email_platform == "M365":
            # Standard M365 pricing
            # Always include these placeholders with default values
            placeholders["fixed_cost_rate"] = f"${pricing.get('fixed_cost_rate', 7500):,}"
            placeholders["tenant_analysis"] = f"${pricing.get('tenant_analysis', 7500):,}"

            # Message extraction cost
            message_extraction_cost = self.message_extraction_cost if self.include_message_extraction else 0
            placeholders["m365_message_extraction"] = f"${message_extraction_cost:,}"
            placeholders["message_extraction_cost"] = f"${message_extraction_cost:,}"  # Alternative name

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('fixed_cost_rate', 7500)
            total_cost = base_cost + message_extraction_cost + triage_cost + summary_cost
            placeholders["m365_total_cost"] = f"${pricing.get('m365_total_cost', total_cost):,}"
            placeholders["total_cost"] = f"${total_cost:,}"  # Alternative name
            placeholders["total_costs_with_optional"] = f"${total_cost:,}"  # Requested placeholder

            # Baker & Hostetler M365 pricing
            # Always include these placeholders with default values
            placeholders["baker_m365_fixed_cost_rate"] = f"${pricing.get('baker_m365_fixed_cost_rate', 7500):,}"
            placeholders["baker_m365_num_users"] = f"{self.tenant_size:,}"
            placeholders["baker_m365_num_mailboxes"] = f"{self.mailbox_count:,}"
            placeholders["baker_m365_tenant_analysis"] = f"${pricing.get('baker_m365_tenant_analysis', 7500):,}"
            placeholders["baker_m365_triage_num"] = f"{self.triage_count}"

            # Message extraction cost
            message_extraction_cost = self.message_extraction_cost if self.include_message_extraction else 0
            placeholders["baker_m365_message_extraction"] = f"${message_extraction_cost:,}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["baker_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["baker_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('baker_m365_fixed_cost_rate', 7500)
            total_cost = base_cost + message_extraction_cost + triage_cost + summary_cost
            placeholders["baker_m365_total_cost"] = f"${pricing.get('baker_m365_total_cost', total_cost):,}"
            placeholders["baker_total_costs_with_optional"] = f"${total_cost:,}"

            # Dykema Gossett M365 pricing
            # Always include these placeholders with default values
            placeholders["dykema_m365_fixed_cost_rate"] = f"${pricing.get('dykema_m365_fixed_cost_rate', 7500):,}"
            placeholders["dykema_m365_num_users"] = f"{self.tenant_size:,}"
            placeholders["dykema_m365_num_mailboxes"] = f"{self.mailbox_count:,}"
            placeholders["dykema_m365_tenant_analysis"] = f"${pricing.get('dykema_m365_tenant_analysis', 7500):,}"
            placeholders["dykema_m365_triage_num"] = f"{self.triage_count}"

            # Message extraction cost
            message_extraction_cost = self.message_extraction_cost if self.include_message_extraction else 0
            placeholders["dykema_m365_message_extraction"] = f"${message_extraction_cost:,}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["dykema_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["dykema_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('dykema_m365_fixed_cost_rate', 7500)
            total_cost = base_cost + message_extraction_cost + triage_cost + summary_cost
            placeholders["dykema_m365_total_cost"] = f"${pricing.get('dykema_m365_total_cost', total_cost):,}"
            placeholders["dykema_total_costs_with_optional"] = f"${total_cost:,}"

            # Woods Rogers M365 pricing
            # Always include these placeholders with default values
            placeholders["woods_m365_fixed_cost_rate"] = f"${pricing.get('woods_m365_fixed_cost_rate', 7500):,}"
            placeholders["woods_m365_num_users"] = f"{self.tenant_size:,}"
            placeholders["woods_m365_num_mailboxes"] = f"{self.mailbox_count:,}"
            placeholders["woods_m365_tenant_analysis"] = f"${pricing.get('woods_m365_tenant_analysis', 7500):,}"
            placeholders["woods_triage_num"] = f"{self.triage_count}"

            # Message extraction cost
            message_extraction_cost = self.message_extraction_cost if self.include_message_extraction else 0
            placeholders["woods_m365_message_extraction"] = f"${message_extraction_cost:,}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["woods_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["woods_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('woods_m365_fixed_cost_rate', 7500)
            total_cost = base_cost + message_extraction_cost + triage_cost + summary_cost
            placeholders["woods_m365_total_cost"] = f"${pricing.get('woods_m365_total_cost', total_cost):,}"
            placeholders["woods_total_costs_with_optional"] = f"${total_cost:,}"

            # Beazley M365 pricing
            # Always include these placeholders with default values
            placeholders["beazley_m365_fixed_cost_rate"] = f"${pricing.get('beazley_m365_fixed_cost_rate', 7500):,}"
            placeholders["beazley_m365_num_users"] = f"{self.tenant_size:,}"
            placeholders["beazley_m365_num_mailboxes"] = f"1 - {self.mailbox_count:,}"
            placeholders["beazley_m365_tenant_analysis"] = f"${pricing.get('beazley_m365_tenant_analysis', 7500):,}"

            # Message extraction cost
            message_extraction_cost = self.message_extraction_cost if self.include_message_extraction else 0
            placeholders["beazley_m365_message_extraction"] = f"${message_extraction_cost:,}"

            # Total cost
            base_cost = pricing.get('beazley_m365_fixed_cost_rate', 7500)
            total_cost = base_cost + message_extraction_cost
            placeholders["beazley_m365_total_cost"] = f"${pricing.get('beazley_m365_total_cost', total_cost):,}"
            placeholders["beazley_total_costs_with_optional"] = f"${total_cost:,}"

            # Chubb M365 pricing
            # Always include these placeholders with default values
            placeholders["chubb_m365_fixed_cost_rate"] = f"${pricing.get('chubb_m365_fixed_cost_rate', 7500):,}"
            placeholders["chubb_m365_num_users"] = f"{self.tenant_size:,}"
            placeholders["chubb_m365_num_mailboxes"] = f"1 - {self.mailbox_count:,}"
            placeholders["chubb_m365_tenant_analysis"] = f"${pricing.get('chubb_m365_tenant_analysis', 7500):,}"
            placeholders["chubb_m365_triage_num"] = f"{self.triage_count}"

            # Mandatory fee for Chubb
            placeholders["chubb_m365_mandatory_fee"] = "$599.00"

            # Message extraction cost
            message_extraction_cost = self.message_extraction_cost if self.include_message_extraction else 0
            placeholders["chubb_m365_message_extraction"] = f"${message_extraction_cost:,}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["chubb_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["chubb_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('chubb_m365_fixed_cost_rate', 7500)
            # Add mandatory fee to total cost
            total_cost = base_cost + message_extraction_cost + triage_cost + summary_cost + 599
            placeholders["chubb_m365_total_cost"] = f"${pricing.get('chubb_m365_total_cost', total_cost):,}"
            placeholders["chubb_total_costs_with_optional"] = f"${total_cost:,}"

            # Coalition M365 pricing
            # Always include these placeholders with default values
            placeholders["coalition_m365_fixed_cost_rate"] = f"${pricing.get('coalition_m365_fixed_cost_rate', 7500):,}"
            placeholders["coalition_m365_num_users"] = f"{self.tenant_size:,}"
            placeholders["coalition_m365_num_mailboxes"] = f"1 - {self.mailbox_count:,}"
            placeholders["coalition_m365_tenant_analysis"] = f"${pricing.get('coalition_m365_tenant_analysis', 7500):,}"
            placeholders["coalition_m365_triage_num"] = f"{self.triage_count}"

            # Message extraction cost
            message_extraction_cost = self.message_extraction_cost if self.include_message_extraction else 0
            placeholders["coalition_m365_message_extraction"] = f"${message_extraction_cost:,}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["coalition_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost - Coalition uses $750 instead of $1000
            summary_cost = 750 if self.include_triage_analysis else 0
            placeholders["coalition_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('coalition_m365_fixed_cost_rate', 7500)
            total_cost = base_cost + message_extraction_cost + triage_cost + summary_cost
            placeholders["coalition_m365_total_cost"] = f"${pricing.get('coalition_m365_total_cost', total_cost):,}"
            placeholders["coalition_total_costs_with_optional"] = f"${total_cost:,}"

        elif self.email_platform == "Google Cloud Platform":
            # Standard GCP pricing
            # Always include these placeholders with default values
            placeholders["fixed_gcp_cost_rate"] = f"${pricing.get('fixed_gcp_cost_rate', 8500):,}"
            placeholders["num_gcp_users"] = f"{self.tenant_size:,}"
            placeholders["gcp_num_mailboxes"] = f"{self.mailbox_count:,}"  # Added mailbox count
            placeholders["gcp_tenant_analysis"] = f"${pricing.get('gcp_tenant_analysis', 8500):,}"
            placeholders["gcp_triage_num"] = f"{self.triage_count}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["gcp_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["gcp_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('fixed_gcp_cost_rate', 8500)
            total_cost = base_cost + triage_cost + summary_cost
            placeholders["gcp_total_cost"] = f"${pricing.get('gcp_total_cost', total_cost):,}"
            placeholders["gcp_total_costs_with_optional"] = f"${total_cost:,}"

            # Baker & Hostetler GCP pricing
            # Always include these placeholders with default values
            placeholders["baker_gcp_fixed_cost_rate"] = f"${pricing.get('baker_gcp_fixed_cost_rate', 8500):,}"
            placeholders["baker_gcp_num_users"] = f"{self.tenant_size:,}"
            placeholders["baker_gcp_num_mailboxes"] = f"{self.mailbox_count:,}"  # Added mailbox count
            placeholders["baker_gcp_tenant_analysis"] = f"${pricing.get('baker_gcp_tenant_analysis', 8500):,}"
            placeholders["baker_gcp_triage_num"] = f"{self.triage_count}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["baker_gcp_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["baker_gcp_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('baker_gcp_fixed_cost_rate', 8500)
            total_cost = base_cost + triage_cost + summary_cost
            placeholders["baker_gcp_total_cost"] = f"${pricing.get('baker_gcp_total_cost', total_cost):,}"
            placeholders["baker_gcp_total_costs_with_optional"] = f"${total_cost:,}"

            # Dykema Gossett GCP pricing
            # Always include these placeholders with default values
            placeholders["dykema_gcp_fixed_cost_rate"] = f"${pricing.get('dykema_gcp_fixed_cost_rate', 8500):,}"
            placeholders["dykema_gcp_num_users"] = f"{self.tenant_size:,}"
            placeholders["dykema_gcp_num_mailboxes"] = f"{self.mailbox_count:,}"  # Added mailbox count
            placeholders["dykema_gcp_tenant_analysis"] = f"${pricing.get('dykema_gcp_tenant_analysis', 8500):,}"
            placeholders["dykema_gcp_triage_num"] = f"{self.triage_count}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["dykema_gcp_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["dykema_gcp_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('dykema_gcp_fixed_cost_rate', 8500)
            total_cost = base_cost + triage_cost + summary_cost
            placeholders["dykema_gcp_total_cost"] = f"${pricing.get('dykema_gcp_total_cost', total_cost):,}"
            placeholders["dykema_gcp_total_costs_with_optional"] = f"${total_cost:,}"

            # Chubb GCP pricing
            # Always include these placeholders with default values
            placeholders["chubb_gcp_fixed_cost_rate"] = f"${pricing.get('chubb_gcp_fixed_cost_rate', 8500):,}"
            placeholders["chubb_gcp_num_users"] = f"{self.tenant_size:,}"
            placeholders["chubb_gcp_num_mailboxes"] = f"1 - {self.mailbox_count:,}"
            placeholders["chubb_gcp_tenant_analysis"] = f"${pricing.get('chubb_gcp_tenant_analysis', 8500):,}"
            placeholders["chubb_gcp_triage_num"] = f"{self.triage_count}"

            # Mandatory fee for Chubb
            placeholders["chubb_gcp_mandatory_fee"] = "$599.00"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["chubb_gcp_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost
            summary_cost = 1000 if self.include_triage_analysis else 0
            placeholders["chubb_gcp_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('chubb_gcp_fixed_cost_rate', 8500)
            # Add mandatory fee to total cost
            total_cost = base_cost + triage_cost + summary_cost + 599
            placeholders["chubb_gcp_total_cost"] = f"${pricing.get('chubb_gcp_total_cost', total_cost):,}"
            placeholders["chubb_gcp_total_costs_with_optional"] = f"${total_cost:,}"

            # Coalition GCP pricing
            # Always include these placeholders with default values
            placeholders["coalition_gcp_fixed_cost_rate"] = f"${pricing.get('coalition_gcp_fixed_cost_rate', 8500):,}"
            placeholders["coalition_gcp_num_users"] = f"{self.tenant_size:,}"
            placeholders["coalition_gcp_num_mailboxes"] = f"{self.mailbox_count:,}"  # Added mailbox count
            placeholders["coalition_gcp_tenant_analysis"] = f"${pricing.get('coalition_gcp_tenant_analysis', 8500):,}"
            placeholders["coalition_gcp_triage_num"] = f"{self.triage_count}"

            # Triage analysis cost
            triage_cost = 1500 * self.triage_count if self.include_triage_analysis else 0
            placeholders["coalition_gcp_triage_cost"] = f"${triage_cost:,}"

            # Executive summary cost - Coalition uses $750 instead of $1000
            summary_cost = 750 if self.include_triage_analysis else 0
            placeholders["coalition_gcp_summary_cost"] = f"${summary_cost:,}"

            # Total cost
            base_cost = pricing.get('coalition_gcp_fixed_cost_rate', 8500)
            total_cost = base_cost + triage_cost + summary_cost
            placeholders["coalition_gcp_total_cost"] = f"${pricing.get('coalition_gcp_total_cost', total_cost):,}"
            placeholders["coalition_gcp_total_costs_with_optional"] = f"${total_cost:,}"

        elif self.email_platform == "Exchange":
            # Standard Exchange pricing
            # Always include these placeholders with default values
            placeholders["exchange_custom_pricing"] = "Custom pricing available upon request"
            placeholders["exchange_num_mailboxes"] = f"{self.mailbox_count:,}"  # Added mailbox count
            placeholders["exchange_total_costs_with_optional"] = "Custom pricing available upon request"

            # Chubb Exchange pricing
            # Always include these placeholders with default values
            placeholders["chubb_exchange_custom_pricing"] = "Custom pricing available upon request"
            placeholders["chubb_exchange_num_mailboxes"] = f"{self.mailbox_count:,}"  # Added mailbox count

            # Mandatory fee for Chubb
            placeholders["chubb_exchange_mandatory_fee"] = "$599.00"

            # For Exchange, we'll show the mandatory fee in the total cost
            placeholders["chubb_exchange_total_costs_with_optional"] = "Custom pricing + $599.00 mandatory fee"

        # Add advanced options pricing (always include with default values)
        placeholders["hybrid_cost"] = f"${pricing.get('hybrid_cost', 0):,}"
        placeholders["export_cost"] = f"${pricing.get('export_cost', 0):,}"
        placeholders["analysis_cost"] = f"${pricing.get('analysis_cost', 0):,}"
        placeholders["timeframe_cost"] = f"${pricing.get('timeframe_cost', 0):,}"

        # Use the adjusted total cost if available, otherwise use the base total cost
        if "adjusted_total_cost" in pricing:
            placeholders["adjusted_total_cost"] = f"${pricing['adjusted_total_cost']:,}"
        elif self.email_platform == "M365":
            placeholders["adjusted_total_cost"] = placeholders["m365_total_cost"]
        elif self.email_platform == "Google Cloud Platform":
            placeholders["adjusted_total_cost"] = placeholders["gcp_total_cost"]
        else:
            placeholders["adjusted_total_cost"] = "Custom pricing available upon request"

        return placeholders
