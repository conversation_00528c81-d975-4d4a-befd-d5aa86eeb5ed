@echo off
setlocal enabledelayedexpansion

REM Set version number
set VERSION=1.1.3

echo Creating Minimal PACE Package v%VERSION%
echo =====================================
echo.

REM Set up directories
set SOURCE_DIR=%CD%
set PACKAGE_DIR=%SOURCE_DIR%\PACE_Minimal_%VERSION%

REM Create clean directory
if exist "%PACKAGE_DIR%" (
    echo Removing existing package directory...
    rmdir /s /q "%PACKAGE_DIR%"
)
mkdir "%PACKAGE_DIR%"

REM Create requirements.txt file
echo Creating requirements.txt file...
(
echo PySide6==6.6.1
echo python-docx==1.1.0
echo docxtpl==0.16.7
echo jinja2==3.1.3
echo babel==2.14.0
echo pytz==2024.1
echo lxml==5.1.0
echo markupsafe==2.1.5
) > "%PACKAGE_DIR%\requirements.txt"

REM Create README for the package
echo Creating package README...
(
echo # PACE v%VERSION% - Minimal Package
echo.
echo ## Setup Instructions
echo.
echo 1. Install Python 3.9+ from https://www.python.org/downloads/
echo 2. Open a command prompt in this directory
echo 3. Install the required packages:
echo    ```
echo    pip install -r requirements.txt
echo    ```
echo 4. Run the application:
echo    ```
echo    python main.py
echo    ```
) > "%PACKAGE_DIR%\README.txt"

REM Copy only essential files
echo Copying essential files...

REM Copy main.py
echo Copying main.py...
copy "%SOURCE_DIR%\main.py" "%PACKAGE_DIR%\"

REM Copy essential module directories
echo Copying essential module directories...

REM Copy utils directory (essential for document generation)
if exist "%SOURCE_DIR%\utils" (
    echo Copying utils directory...
    mkdir "%PACKAGE_DIR%\utils"
    copy "%SOURCE_DIR%\utils\*.py" "%PACKAGE_DIR%\utils\"
)

REM Copy models directory (essential for data models)
if exist "%SOURCE_DIR%\models" (
    echo Copying models directory...
    mkdir "%PACKAGE_DIR%\models"
    copy "%SOURCE_DIR%\models\*.py" "%PACKAGE_DIR%\models\"
)

REM Copy data directory (essential for carrier and law firm data)
if exist "%SOURCE_DIR%\data" (
    echo Copying data directory...
    mkdir "%PACKAGE_DIR%\data"
    copy "%SOURCE_DIR%\data\*.py" "%PACKAGE_DIR%\data\"
)

REM Copy gui directory (essential for UI)
if exist "%SOURCE_DIR%\gui" (
    echo Copying gui directory...
    mkdir "%PACKAGE_DIR%\gui"
    copy "%SOURCE_DIR%\gui\*.py" "%PACKAGE_DIR%\gui\"
)

REM Copy only necessary template files
echo Copying essential template files...
if exist "%SOURCE_DIR%\templates\base_templates" (
    echo Copying base_templates directory...
    mkdir "%PACKAGE_DIR%\templates"
    xcopy /E /I /Y "%SOURCE_DIR%\templates\base_templates" "%PACKAGE_DIR%\templates\base_templates\"
)

REM Copy essential resources
echo Copying essential resources...
if exist "%SOURCE_DIR%\resources" (
    echo Copying resources directory...
    mkdir "%PACKAGE_DIR%\resources"
    xcopy /E /I /Y "%SOURCE_DIR%\resources\icons" "%PACKAGE_DIR%\resources\icons\"
)

REM Copy PACE icon
if exist "%SOURCE_DIR%\PACE.ico" (
    echo Copying PACE icon...
    copy "%SOURCE_DIR%\PACE.ico" "%PACKAGE_DIR%\"
)

echo.
echo Minimal PACE package created successfully at: %PACKAGE_DIR%
echo.
echo Instructions for your coworker:
echo 1. Unzip the package
echo 2. Follow the instructions in README.txt to set up and run PACE
echo.
pause
