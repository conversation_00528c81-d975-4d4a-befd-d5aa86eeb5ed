"""
Main application file for PACE (Process Automation for Client Engagements).
"""

# Version number
__version__ = "1.1.3"

import sys
import os
import json
import datetime

# Import path resolver for template path resolution
from utils.path_resolver import resolve_template_path, get_base_dir, get_all_template_files
from PySide6.QtWidgets import (QApplication, QMainWindow, QFileDialog, QMessageBox,
                             QStackedWidget, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QStatusBar,
                             QProgressDialog)
from PySide6.QtGui import QIcon, QAction
from PySide6.QtCore import Qt, QSettings

from utils.style_manager import StyleManager

from gui.welcome_screen import WelcomeScreen
from gui.dfir_screen import DFIRScreen
from gui.taci_screen import TACIScreen
from gui.bec_screen import BECScreen
from gui.rr_screen import RRScreen

from models.client import Client

from utils.reliable_document_generator import ReliableDocumentGenerator

class MainWindow(QMainWindow):
    """
    Main window for the PACE application.
    """

    def __init__(self):
        """
        Initialize the main window.
        """
        super().__init__()

        # Initialize settings
        self.settings = QSettings("PACE_App", "PACE")
        self.dark_mode = self.settings.value("dark_mode", False, type=bool)

        # Apply the theme
        self.apply_theme()

        # Set application icon
        try:
            icon_paths = [
                "PACE.ico",  # Current directory
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "PACE.ico"),  # Script directory
                os.path.join(os.path.dirname(sys.executable), "PACE.ico"),  # Executable directory
                "app_icon.ico",  # Fallback to old icon name
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "app_icon.ico"),  # Fallback script directory
                os.path.join(os.path.dirname(sys.executable), "app_icon.ico"),  # Fallback executable directory
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    self.app_icon = QIcon(icon_path)
                    self.setWindowIcon(self.app_icon)
                    print(f"Found and loaded icon from: {icon_path}")
                    break
            else:
                print("Warning: Could not find application icon")
        except Exception as e:
            print(f"Warning: Error loading application icon: {e}")

        self.init_ui()

    def init_ui(self):
        """
        Initialize the UI.
        """
        # Set window properties
        self.setWindowTitle("PACE - Process Automation for Client Engagements")
        self.setMinimumSize(1000, 800)  # Increased minimum size for better visibility
        self.resize(1200, 900)  # Set a default size that works well for most screens

        # Create main layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create header
        header_widget = QWidget()
        header_widget.setObjectName("header_widget")
        header_layout = QHBoxLayout(header_widget)

        # Add title
        header_label = QLabel("PACE")
        header_label.setObjectName("header_label")
        header_layout.addWidget(header_label)

        # Add subtitle
        subtitle_label = QLabel("Process Automation for Client Engagements")
        subtitle_label.setObjectName("subtitle_label")
        header_layout.addWidget(subtitle_label)

        # Add version label
        version_label = QLabel(f"v{__version__}")
        version_label.setObjectName("version_label")
        header_layout.addWidget(version_label)

        # Add spacer
        header_layout.addStretch()

        # Add theme switch button
        self.theme_switch = QPushButton("🌙" if not self.dark_mode else "☀️")
        self.theme_switch.setObjectName("theme_switch")
        self.theme_switch.setFixedSize(30, 30)
        self.theme_switch.clicked.connect(self.toggle_theme)
        self.theme_switch.setToolTip("Toggle Dark/Light Mode")
        header_layout.addWidget(self.theme_switch)

        # Add header to main layout
        main_layout.addWidget(header_widget)

        # Set up the stacked widget
        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)

        # Set the central widget
        self.setCentralWidget(main_widget)

        # Create menu bar
        menu_bar = self.menuBar()

        # Create View menu
        view_menu = menu_bar.addMenu("View")

        # Create theme action
        self.theme_action = QAction("Toggle Dark Mode", self)
        self.theme_action.setCheckable(True)
        self.theme_action.setChecked(self.dark_mode)
        self.theme_action.triggered.connect(self.toggle_theme)
        self.theme_action.setShortcut("Ctrl+D")  # Add keyboard shortcut
        view_menu.addAction(self.theme_action)

        # Create Help menu
        help_menu = menu_bar.addMenu("Help")

        # Create About action
        about_action = QAction("About PACE", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

        # Create README action
        readme_action = QAction("View README", self)
        readme_action.triggered.connect(self.show_readme)
        help_menu.addAction(readme_action)

        # Create Version History action
        version_action = QAction("Version History", self)
        version_action.triggered.connect(self.show_version_history)
        help_menu.addAction(version_action)

        # Create status bar
        status_bar = QStatusBar()
        status_bar.showMessage("Ready")
        self.setStatusBar(status_bar)

        # Create the welcome screen
        self.welcome_screen = WelcomeScreen()
        self.welcome_screen.next_clicked.connect(self.on_welcome_next_clicked)

        # Add the welcome screen to the stacked widget
        self.stacked_widget.addWidget(self.welcome_screen)

        # Set the window size
        self.resize(800, 600)

    def on_welcome_next_clicked(self, client, engagement_type):
        """
        Handle the Next button click on the welcome screen.

        Args:
            client (Client): The client for this engagement
            engagement_type (str): The type of engagement
        """
        # Create the appropriate screen based on the engagement type
        if engagement_type == "DFIR":
            self.engagement_screen = DFIRScreen(client)
            self.engagement_screen.back_clicked.connect(self.on_back_clicked)
            self.engagement_screen.generate_clicked.connect(self.on_generate_clicked)
        elif engagement_type == "TACI":
            self.engagement_screen = TACIScreen(client)
            self.engagement_screen.back_clicked.connect(self.on_back_clicked)
            self.engagement_screen.generate_clicked.connect(self.on_generate_clicked)
        elif engagement_type == "BEC":
            self.engagement_screen = BECScreen(client)
            self.engagement_screen.back_clicked.connect(self.on_back_clicked)
            self.engagement_screen.generate_clicked.connect(self.on_generate_clicked)
        elif engagement_type == "RR":
            self.engagement_screen = RRScreen(client)
            self.engagement_screen.back_clicked.connect(self.on_back_clicked)
            self.engagement_screen.generate_clicked.connect(self.on_generate_clicked)
        else:
            return

        # Add the engagement screen to the stacked widget
        self.stacked_widget.addWidget(self.engagement_screen)

        # Show the engagement screen
        self.stacked_widget.setCurrentWidget(self.engagement_screen)

    def on_back_clicked(self):
        """
        Handle the Back button click on the engagement screen.
        """
        # Show the welcome screen
        self.stacked_widget.setCurrentWidget(self.welcome_screen)

        # Remove the engagement screen
        self.stacked_widget.removeWidget(self.engagement_screen)

    def on_generate_clicked(self, engagement):
        """
        Handle the Generate Documents button click on the engagement screen.

        Args:
            engagement: The engagement object
        """
        # Validate the engagement
        if not engagement.is_valid():
            QMessageBox.warning(
                self,
                "Invalid Engagement",
                "Please fill in all required fields."
            )
            return

        # Ask the user where to save the documents
        output_dir = QFileDialog.getExistingDirectory(
            self,
            "Select Output Directory",
            os.path.expanduser("~"),
            QFileDialog.ShowDirsOnly
        )

        if not output_dir:
            return

        # Show a progress dialog
        progress = QProgressDialog("Generating documents...", "Cancel", 0, 100, self)
        progress.setWindowTitle("Document Generation")
        progress.setWindowModality(Qt.WindowModal)
        progress.setValue(0)
        progress.show()

        # Generate the documents
        try:
            # Debug output for engagement object
            print(f"DEBUG - Main - Engagement object: {engagement}")
            print(f"DEBUG - Main - Client object: {engagement.client}")
            print(f"DEBUG - Main - Client needs_baa: {getattr(engagement.client, 'needs_baa', 'NOT SET')}")
            print(f"DEBUG - Main - Client needs_dpa: {getattr(engagement.client, 'needs_dpa', 'NOT SET')}")

            # Create the document generator
            # Use the reliable document generator for consistent document generation
            print("Using ReliableDocumentGenerator for consistent document generation")
            document_generator = ReliableDocumentGenerator(engagement, output_dir)

            # Update progress
            progress.setValue(10)
            QApplication.processEvents()

            # Generate the documents
            generated_documents = document_generator.generate_documents()

            # Debug output for generated documents
            print(f"DEBUG - Main - Generated documents count: {len(generated_documents)}")
            for i, doc in enumerate(generated_documents):
                print(f"DEBUG - Main - Document {i+1}: {doc}")

            # Update progress
            progress.setValue(90)
            QApplication.processEvents()

            # Close the progress dialog
            progress.close()

            # Show a success message with details
            message = f"Successfully generated {len(generated_documents)} document(s):\n\n"

            # Debug output for success message
            print(f"DEBUG - Main - Building success message for {len(generated_documents)} documents")

            # Verify that all documents exist
            for i, doc in enumerate(generated_documents):
                doc_name = os.path.basename(doc)
                print(f"DEBUG - Main - Adding document {i+1} to success message: {doc_name}")
                if os.path.exists(doc):
                    print(f"DEBUG - Main - Document exists: {doc}")
                    message += f"- {doc_name}\n"
                else:
                    print(f"DEBUG - Main - Document does not exist: {doc}")
                    message += f"- {doc_name} (FILE NOT FOUND)\n"

            message += "\nWould you like to open the output directory?"

            reply = QMessageBox.question(
                self,
                "Documents Generated",
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            # Open the output directory if requested
            if reply == QMessageBox.Yes:
                import subprocess
                try:
                    # Get the actual output directory from the document generator
                    actual_output_dir = output_dir  # Use the directory selected by the user
                    print(f"DEBUG - Main - Opening directory: {actual_output_dir}")

                    # Make sure the directory exists
                    if not os.path.exists(actual_output_dir):
                        print(f"WARNING: Selected directory {actual_output_dir} does not exist, using document generator output directory instead")
                        actual_output_dir = document_generator.output_dir

                    # Open the directory using the appropriate method for the platform
                    # Since we're on Windows, use explorer
                    subprocess.Popen(['explorer', actual_output_dir])

                    # Just open the directory and let the user open the documents manually
                    # This is more reliable than trying to open each document programmatically
                    print(f"DEBUG - Main - Opening directory only, not attempting to open documents individually")
                except Exception as open_error:
                    print(f"Error opening directory: {open_error}")

                # Show a success message with instructions
                QMessageBox.information(self, "Documents Generated",
                                      f"Documents have been generated successfully in:\n{actual_output_dir}\n\nThe folder has been opened for you. Please open the documents manually from the folder.")

            # Show the welcome screen
            self.stacked_widget.setCurrentWidget(self.welcome_screen)

            # Remove the engagement screen
            self.stacked_widget.removeWidget(self.engagement_screen)
        except Exception as e:
            # Close the progress dialog
            progress.close()

            # Show detailed error message
            import traceback
            error_details = traceback.format_exc()

            error_message = f"Error generating documents: {str(e)}\n\n"
            error_message += "Would you like to see the detailed error message?"

            reply = QMessageBox.question(
                self,
                "Error",
                error_message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Show detailed error dialog
                from PySide6.QtWidgets import QDialog, QTextEdit, QVBoxLayout

                error_dialog = QDialog(self)
                error_dialog.setWindowTitle("Error Details")
                error_dialog.resize(600, 400)

                text_edit = QTextEdit(error_dialog)
                text_edit.setReadOnly(True)
                text_edit.setPlainText(error_details)

                layout = QVBoxLayout(error_dialog)
                layout.addWidget(text_edit)

                error_dialog.exec()


    def toggle_theme(self):
        """
        Toggle between light and dark mode.
        """
        self.dark_mode = not self.dark_mode
        self.settings.setValue("dark_mode", self.dark_mode)

        # Update the menu action
        if hasattr(self, 'theme_action'):
            self.theme_action.setChecked(self.dark_mode)

        # Apply the theme
        self.apply_theme()

    def apply_theme(self):
        """
        Apply the current theme to the application.
        """
        if self.dark_mode:
            self.setStyleSheet(StyleManager.get_dark_stylesheet())
        else:
            self.setStyleSheet(StyleManager.get_light_stylesheet())

    def show_about_dialog(self):
        """
        Show the About dialog.
        """
        about_text = f"""<h2>PACE - Process Automation for Client Engagements</h2>
        <p>Version: {__version__}</p>
        <p>PACE is a document generation tool designed to streamline the creation of engagement documents for cybersecurity services.</p>
        <p>Developed by: Chad Polliard</p>"""

        QMessageBox.about(self, "About PACE", about_text)

    def show_readme(self):
        """
        Show the README file in a dialog.
        """
        try:
            # Read the README file
            with open("README.md", "r") as f:
                readme_content = f.read()

            # Create a dialog to display the README
            from PySide6.QtWidgets import QDialog, QTextEdit, QVBoxLayout
            from PySide6.QtCore import Qt

            readme_dialog = QDialog(self)
            readme_dialog.setWindowTitle("PACE README")
            readme_dialog.resize(800, 600)

            text_edit = QTextEdit(readme_dialog)
            text_edit.setReadOnly(True)
            text_edit.setPlainText(readme_content)
            text_edit.setLineWrapMode(QTextEdit.WidgetWidth)

            layout = QVBoxLayout(readme_dialog)
            layout.addWidget(text_edit)

            readme_dialog.exec()
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Could not open README file: {str(e)}")

    def show_version_history(self):
        """
        Show the version history.
        """
        version_text = """<h2>PACE Version History</h2>
        <h3>Version 1.1.3</h3>
        <ul>
            <li>Added mandatory $599.00 fee to all Chubb SOWs (both DFIR and BEC)</li>
            <li>Fixed proper placeholder replacement for chubb_endpoint_count in Chubb SOW templates</li>
            <li>Fixed template path resolution to prioritize templates from the base_templates directory</li>
            <li>Improved error handling in document generation</li>
        </ul>

        <h3>Version 1.1.2</h3>
        <ul>
            <li>Fixed template path resolution in installed version</li>
            <li>Improved HTML entity decoding for special characters</li>
            <li>Fixed issues with TACI and IR SOW documents not opening</li>
            <li>Enhanced error handling and logging for document generation</li>
        </ul>

        <h3>Version 1.1.1</h3>
        <ul>
            <li>Fixed Chubb template issues with CS code placeholders</li>
            <li>Improved performance with cleanup of unnecessary files</li>
            <li>Updated documentation and README</li>
        </ul>

        <h3>Version 1.1.0</h3>
        <ul>
            <li>Added DFIR engagement support</li>
            <li>Improved carrier-specific features</li>
            <li>Added Beazley-specific ransomware/network intrusion option</li>
            <li>Enhanced document generation with better error handling</li>
            <li>Improved template handling</li>
        </ul>

        <h3>Version 1.0.0</h3>
        <ul>
            <li>Initial release with basic document generation capabilities</li>
            <li>Support for TACI, BEC, and Recovery & Remediation engagements</li>
            <li>Basic carrier and law firm integration</li>
        </ul>"""

        # Create a dialog to display the version history
        from PySide6.QtWidgets import QDialog, QTextBrowser, QVBoxLayout

        version_dialog = QDialog(self)
        version_dialog.setWindowTitle("PACE Version History")
        version_dialog.resize(600, 400)

        text_browser = QTextBrowser(version_dialog)
        text_browser.setHtml(version_text)

        layout = QVBoxLayout(version_dialog)
        layout.addWidget(text_browser)

        version_dialog.exec()


if __name__ == "__main__":
    # Set up logging to file
    import logging
    log_dir = os.path.join(os.path.expanduser("~"), "PACE_logs")
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, "pace_startup.log")
    logging.basicConfig(filename=log_file, level=logging.DEBUG,
                      format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Log startup information
    logging.info("Starting PACE application")
    logging.info(f"Python version: {sys.version}")
    logging.info(f"Current directory: {os.getcwd()}")
    logging.info(f"Script directory: {os.path.dirname(os.path.abspath(__file__))}")
    logging.info(f"Executable directory: {os.path.dirname(sys.executable)}")

    try:
        app = QApplication(sys.argv)

        # Set application icon
        try:
            icon_paths = [
                "PACE.ico",  # Current directory
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "PACE.ico"),  # Script directory
                os.path.join(os.path.dirname(sys.executable), "PACE.ico"),  # Executable directory
                "app_icon.ico",  # Fallback to old icon name
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "app_icon.ico"),  # Fallback script directory
                os.path.join(os.path.dirname(sys.executable), "app_icon.ico"),  # Fallback executable directory
            ]

            # Log icon paths being checked
            for path in icon_paths:
                logging.info(f"Checking for icon at: {path} (exists: {os.path.exists(path)})")

            icon_found = False
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    app_icon = QIcon(icon_path)
                    app.setWindowIcon(app_icon)
                    logging.info(f"Found and loaded icon from: {icon_path}")
                    print(f"Found and loaded application icon from: {icon_path}")
                    icon_found = True
                    break

            if not icon_found:
                logging.warning("Could not find application icon")
                print("Warning: Could not find application icon")
        except Exception as e:
            logging.error(f"Error loading application icon: {e}")
            print(f"Warning: Error loading application icon: {e}")

        # Create and show the main window
        try:
            main_window = MainWindow()
            main_window.show()
            logging.info("Main window created and shown successfully")
            sys.exit(app.exec())
        except Exception as e:
            logging.critical(f"Failed to create or show main window: {e}")
            import traceback
            logging.critical(traceback.format_exc())
            # Show error message box
            from PySide6.QtWidgets import QMessageBox
            error_msg = QMessageBox()
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.setWindowTitle("PACE Startup Error")
            error_msg.setText("Failed to start PACE application")
            error_msg.setInformativeText(f"Error: {str(e)}")
            error_msg.setDetailedText(traceback.format_exc())
            error_msg.exec()
            sys.exit(1)
    except Exception as e:
        # Log any unhandled exceptions
        logging.critical(f"Unhandled exception during startup: {e}")
        import traceback
        logging.critical(traceback.format_exc())

        # Try to show an error dialog if possible
        try:
            from PySide6.QtWidgets import QApplication, QMessageBox
            app = QApplication(sys.argv)
            error_msg = QMessageBox()
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.setWindowTitle("PACE Startup Error")
            error_msg.setText("Failed to start PACE application")
            error_msg.setInformativeText(f"Error: {str(e)}")
            error_msg.setDetailedText(traceback.format_exc())
            error_msg.exec()
        except:
            # If we can't show a GUI error, print to console
            print(f"CRITICAL ERROR: Failed to start PACE application: {e}")
            print(traceback.format_exc())
        sys.exit(1)
