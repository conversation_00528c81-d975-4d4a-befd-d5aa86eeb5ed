"""
Pricing tables for different engagement types.
"""

# DFIR Fixed Fee Pricing based on endpoint count
DFIR_FIXED_FEE_PRICING = {
    (1, 100): 25000,
    (101, 250): 55000,
    (251, 500): 75000,
    (501, 750): 90000,
    (751, 2000): 115000,
    (2001, 3000): 125000,
    (3001, 4000): 135000,
    (4001, 5000): 150000
}

# EDR SOC Monitoring Fee based on endpoint count
EDR_SOC_MONITORING_FEE = {
    (1, 100): 2500,
    (101, 2000): 5000,
    (2001, 5000): 11250,
    (5001, 10000): 22500,
    (10001, 20000): 30000
}

# DFIR Phase 2 hours based on endpoint count (for single price model)
DFIR_PHASE2_HOURS = {
    (1, 25): 35,
    (26, 50): 40,
    (51, 75): 45,
    (76, 100): 50,
    (101, 250): 55,
    (251, 500): 65,
    (501, 750): 70,
    (751, 2000): 90,
    (2001, 3000): 110,
    (3001, 4000): 125,
    (4001, 5000): 140,
    (5001, 10000): 230,
    (10001, 19999): 275,
    (20000, float('inf')): 325
}

# DFIR Phase 3 hours based on endpoint count (for single price model)
DFIR_PHASE3_HOURS = {
    (1, 500): 15,
    (501, 2500): 30,
    (2501, 5000): 35,
    (5001, 10000): 50
}

# DFIR Phase 5 hours based on endpoint count (for single price model)
DFIR_PHASE5_HOURS = {
    (1, 100): 45,
    (101, 500): 55,
    (501, 1000): 65,
    (1001, 5000): 85,
    (5001, float('inf')): 95
}

# Time & Materials total hours based on endpoint count
TIME_MATERIALS_TOTAL_HOURS = {
    (1, 100): 80,
    (101, 500): 120,
    (501, 1000): 160,
    (1001, 2000): 200,
    (2001, 5000): 240,
    (5001, float('inf')): 300
}

# Time & Materials phase hours based on endpoint count
TIME_MATERIALS_PHASE1_HOURS = {
    (1, 100): 8,
    (101, 500): 12,
    (501, 1000): 16,
    (1001, 2000): 20,
    (2001, 5000): 24,
    (5001, float('inf')): 30
}

TIME_MATERIALS_PHASE2_HOURS = {
    (1, 100): 24,
    (101, 500): 36,
    (501, 1000): 48,
    (1001, 2000): 60,
    (2001, 5000): 72,
    (5001, float('inf')): 90
}

TIME_MATERIALS_PHASE3_HOURS = {
    (1, 100): 16,
    (101, 500): 24,
    (501, 1000): 32,
    (1001, 2000): 40,
    (2001, 5000): 48,
    (5001, float('inf')): 60
}

TIME_MATERIALS_PHASE4_HOURS = {
    (1, 100): 16,
    (101, 500): 24,
    (501, 1000): 32,
    (1001, 2000): 40,
    (2001, 5000): 48,
    (5001, float('inf')): 60
}

TIME_MATERIALS_PHASE5_HOURS = {
    (1, 100): 8,
    (101, 500): 12,
    (501, 1000): 16,
    (1001, 2000): 20,
    (2001, 5000): 24,
    (5001, float('inf')): 30
}

TIME_MATERIALS_PHASE6_HOURS = {
    (1, 100): 8,
    (101, 500): 12,
    (501, 1000): 16,
    (1001, 2000): 20,
    (2001, 5000): 24,
    (5001, float('inf')): 30
}

# DFIR Phase 2 hours range based on endpoint count (for range price model)
DFIR_PHASE2_HOURS_RANGE = {
    (1, 25): (25, 45),
    (26, 50): (30, 50),
    (51, 75): (35, 55),
    (76, 100): (45, 60),
    (101, 250): (50, 65),
    (251, 500): (55, 70),
    (501, 750): (60, 75),
    (751, 2000): (80, 100),
    (2001, 3000): (100, 120),
    (3001, 4000): (115, 140),
    (4001, 5000): (130, 150),
    (5001, 10000): (195, 255),
    (10001, 19999): (250, 300),
    (20000, float('inf')): (275, 350)
}

# DFIR Phase 3 hours range based on endpoint count (for range price model)
DFIR_PHASE3_HOURS_RANGE = {
    (1, 500): (10, 15),
    (501, 2500): (20, 30),
    (2501, 5000): (25, 35),
    (5001, 10000): (35, 50)
}

# DFIR Phase 5 hours range based on endpoint count (for range price model)
DFIR_PHASE5_HOURS_RANGE = {
    (1, 100): (25, 45),
    (101, 500): (35, 55),
    (501, 1000): (45, 65),
    (1001, 5000): (65, 85),
    (5001, float('inf')): (85, 95)
}

# Beazley Fixed Fee Pricing based on endpoint count
BEAZLEY_FIXED_FEE_PRICING = {
    "no_onsite": {
        (1, 25): 25000,
        (26, 100): 32250,
        (101, 250): 45000,
        (251, 500): 52000,
        (501, 1000): 95000,
        (1001, 1500): 105000,
        (1501, 2000): 110000
    },
    "with_onsite": {
        (1, 25): 27500,
        (26, 100): 37250,
        (101, 250): 50000,
        (251, 500): 60000,
        (501, 1000): 105000,
        (1001, 1500): 115000,
        (1501, 2000): 120000
    }
}

# BEC M365 Pricing based on tenant size and mailbox count
BEC_M365_PRICING = {
    "1-5": {
        (1, 500): {"standard": 5000, "e5": 7500},
        (501, 1000): {"standard": 7500, "e5": 10000},
        (1001, 2000): {"standard": 10000, "e5": 12500}
    },
    "1-10": {
        (1, 500): {"standard": 7500, "e5": 10000},
        (501, 1000): {"standard": 10000, "e5": 12500},
        (1001, 2000): {"standard": 12500, "e5": 15000}
    },
    "1-15": {
        (1, 500): {"standard": 10000, "e5": 12500},
        (501, 1000): {"standard": 12500, "e5": 15000},
        (1001, 2000): {"standard": 15000, "e5": 17500}
    }
}

# BEC GCP Pricing based on tenant size and mailbox count
BEC_GCP_PRICING = {
    "1-5": {
        (1, 500): 7500,
        (501, 1000): 10000,
        (1001, 2000): 12500
    },
    "1-10": {
        (1, 500): 10000,
        (501, 1000): 12500,
        (1001, 2000): 15000
    },
    "1-15": {
        (1, 500): 12500,
        (501, 1000): 15000,
        (1001, 2000): 17500
    },
    "1-20": {
        (1, 500): 15000,
        (501, 1000): 17500,
        (1001, 2000): 20000,
        (2001, float('inf')): 22500
    }
}

# Coalition BEC M365 Pricing
COALITION_BEC_M365_PRICING = {
    "1-5": {
        (1, 250): {"standard": 3500, "e5": 5000},
        (251, 500): {"standard": 5000, "e5": 7500},
        (501, 1000): {"standard": 7500, "e5": 10000},
        (1001, 2000): {"standard": 10000, "e5": 12500}
    },
    "1-10": {
        (1, 250): {"standard": 5000, "e5": 7500},
        (251, 500): {"standard": 7500, "e5": 10000},
        (501, 1000): {"standard": 10000, "e5": 12500},
        (1001, 2000): {"standard": 12500, "e5": 15000}
    },
    "1-15": {
        (1, 250): {"standard": 7500, "e5": 10000},
        (251, 500): {"standard": 10000, "e5": 12500},
        (501, 1000): {"standard": 12500, "e5": 15000},
        (1001, 2000): {"standard": 15000, "e5": 17500}
    }
}

# Coalition BEC GCP Pricing
COALITION_BEC_GCP_PRICING = {
    "1-5": {
        (1, 500): 6000,
        (501, 1000): 8500,
        (1001, 2000): 11000
    },
    "1-10": {
        (1, 500): 8500,
        (501, 1000): 11000,
        (1001, 2000): 13500
    },
    "1-15": {
        (1, 500): 11000,
        (501, 1000): 13500,
        (1001, 2000): 16000
    },
    "1-20": {
        (1, 500): 15000,
        (501, 1000): 17500,
        (1001, 2000): 20000,
        (2001, float('inf')): 22500
    }
}

# Beazley BEC M365 Pricing
BEAZLEY_BEC_M365_PRICING = {
    (1, 5): 7500,
    (6, 10): 10000,
    (11, 25): 20000
}

def get_fixed_fee_price(endpoint_count):
    """
    Get the fixed fee price based on endpoint count.

    Args:
        endpoint_count (int): The number of endpoints

    Returns:
        int: The fixed fee price
    """
    for (min_count, max_count), price in DFIR_FIXED_FEE_PRICING.items():
        if min_count <= endpoint_count <= max_count:
            return price
    return None  # For endpoint counts beyond the defined ranges

def get_edr_soc_fee(endpoint_count):
    """
    Get the EDR SOC monitoring fee based on endpoint count.

    Args:
        endpoint_count (int): The number of endpoints

    Returns:
        int: The EDR SOC monitoring fee
    """
    for (min_count, max_count), fee in EDR_SOC_MONITORING_FEE.items():
        if min_count <= endpoint_count <= max_count:
            return fee
    return None  # For endpoint counts beyond the defined ranges

def get_phase_hours(phase, endpoint_count, is_range=False):
    """
    Get the hours for a specific phase based on endpoint count.

    Args:
        phase (int): The phase number (2, 3, or 5)
        endpoint_count (int): The number of endpoints
        is_range (bool): Whether to return a range of hours

    Returns:
        int or tuple: The hours for the phase, or a tuple of (min_hours, max_hours) if is_range is True
    """
    if phase == 2:
        table = DFIR_PHASE2_HOURS_RANGE if is_range else DFIR_PHASE2_HOURS
    elif phase == 3:
        table = DFIR_PHASE3_HOURS_RANGE if is_range else DFIR_PHASE3_HOURS
    elif phase == 5:
        table = DFIR_PHASE5_HOURS_RANGE if is_range else DFIR_PHASE5_HOURS
    else:
        return None

    for (min_count, max_count), hours in table.items():
        if min_count <= endpoint_count <= max_count:
            return hours
    return None  # For endpoint counts beyond the defined ranges

def get_beazley_fixed_fee_price(endpoint_count, has_onsite):
    """
    Get the Beazley fixed fee price based on endpoint count and whether there are onsite collections.

    Args:
        endpoint_count (int): The number of endpoints
        has_onsite (bool): Whether there are onsite collections

    Returns:
        int: The fixed fee price
    """
    table = BEAZLEY_FIXED_FEE_PRICING["with_onsite"] if has_onsite else BEAZLEY_FIXED_FEE_PRICING["no_onsite"]

    for (min_count, max_count), price in table.items():
        if min_count <= endpoint_count <= max_count:
            return price
    return None  # For endpoint counts beyond the defined ranges

def get_bec_m365_price(mailbox_range, tenant_size, is_e5=False):
    """
    Get the BEC M365 price based on mailbox range and tenant size.

    Args:
        mailbox_range (str): The mailbox range ("1-5", "1-10", or "1-15")
        tenant_size (int): The tenant size
        is_e5 (bool): Whether the tenant has E5 licenses

    Returns:
        int: The BEC M365 price
    """
    if mailbox_range not in BEC_M365_PRICING:
        return None

    for (min_size, max_size), price_dict in BEC_M365_PRICING[mailbox_range].items():
        if min_size <= tenant_size <= max_size:
            return price_dict["e5"] if is_e5 else price_dict["standard"]
    return None  # For tenant sizes beyond the defined ranges

def get_bec_gcp_price(mailbox_range, tenant_size):
    """
    Get the BEC GCP price based on mailbox range and tenant size.

    Args:
        mailbox_range (str): The mailbox range ("1-5", "1-10", "1-15", or "1-20")
        tenant_size (int): The tenant size

    Returns:
        int: The BEC GCP price
    """
    if mailbox_range not in BEC_GCP_PRICING:
        return None

    for (min_size, max_size), price in BEC_GCP_PRICING[mailbox_range].items():
        if min_size <= tenant_size <= max_size:
            return price
    return None  # For tenant sizes beyond the defined ranges

def get_coalition_bec_m365_price(mailbox_range, tenant_size, is_e5=False):
    """
    Get the Coalition BEC M365 price based on mailbox range and tenant size.

    Args:
        mailbox_range (str): The mailbox range ("1-5", "1-10", or "1-15")
        tenant_size (int): The tenant size
        is_e5 (bool): Whether the tenant has E5 licenses

    Returns:
        int: The Coalition BEC M365 price
    """
    if mailbox_range not in COALITION_BEC_M365_PRICING:
        return None

    for (min_size, max_size), price_dict in COALITION_BEC_M365_PRICING[mailbox_range].items():
        if min_size <= tenant_size <= max_size:
            return price_dict["e5"] if is_e5 else price_dict["standard"]
    return None  # For tenant sizes beyond the defined ranges

def get_coalition_bec_gcp_price(mailbox_range, tenant_size):
    """
    Get the Coalition BEC GCP price based on mailbox range and tenant size.

    Args:
        mailbox_range (str): The mailbox range ("1-5", "1-10", "1-15", or "1-20")
        tenant_size (int): The tenant size

    Returns:
        int: The Coalition BEC GCP price
    """
    if mailbox_range not in COALITION_BEC_GCP_PRICING:
        return None

    for (min_size, max_size), price in COALITION_BEC_GCP_PRICING[mailbox_range].items():
        if min_size <= tenant_size <= max_size:
            return price
    return None  # For tenant sizes beyond the defined ranges

def get_beazley_bec_m365_price(mailbox_count):
    """
    Get the Beazley BEC M365 price based on mailbox count.

    Args:
        mailbox_count (int): The number of mailboxes

    Returns:
        int: The Beazley BEC M365 price
    """
    for (min_count, max_count), price in BEAZLEY_BEC_M365_PRICING.items():
        if min_count <= mailbox_count <= max_count:
            return price
    return None  # For mailbox counts beyond the defined ranges
