"""
Base engagement model for PACE application.
"""

from datetime import datetime

class Engagement:
    """
    Base class for all engagement types in the PACE application.
    """

    def __init__(self, client=None, engagement_type=""):
        """
        Initialize a new Engagement object.

        Args:
            client (Client): The client for this engagement
            engagement_type (str): The type of engagement (DFIR, TACI, BEC, RR)
        """
        self.client = client
        self.engagement_type = engagement_type
        self.date = datetime.now()
        self.needs_dpa = False
        self.needs_baa = False

        # Set needs_msa to True if a law firm is specified
        self.needs_msa = client is not None and client.law_firm and client.law_firm.strip() != ""

    def is_valid(self):
        """
        Check if the engagement data is valid.

        Returns:
            bool: True if the engagement data is valid, False otherwise
        """
        return self.client is not None and self.client.is_valid() and bool(self.engagement_type)

    def to_dict(self):
        """
        Convert the engagement data to a dictionary.

        Returns:
            dict: A dictionary containing the engagement data
        """
        return {
            "client": self.client.to_dict() if self.client else None,
            "engagement_type": self.engagement_type,
            "date": self.date.strftime("%Y-%m-%d"),
            "needs_dpa": self.needs_dpa,
            "needs_baa": self.needs_baa,
            "needs_msa": self.needs_msa
        }

    @classmethod
    def from_dict(cls, data, client=None):
        """
        Create an Engagement object from a dictionary.

        Args:
            data (dict): A dictionary containing engagement data
            client (Client): The client for this engagement

        Returns:
            Engagement: A new Engagement object
        """
        engagement = cls(
            client=client,
            engagement_type=data.get("engagement_type", "")
        )

        if "date" in data:
            try:
                engagement.date = datetime.strptime(data["date"], "%Y-%m-%d")
            except ValueError:
                pass

        engagement.needs_dpa = data.get("needs_dpa", False)
        engagement.needs_baa = data.get("needs_baa", False)
        engagement.needs_msa = data.get("needs_msa", False)

        return engagement

    def get_formatted_date(self, format_str="%Y%m%d"):
        """
        Get the engagement date formatted as a string.

        Args:
            format_str (str): The format string to use

        Returns:
            str: The formatted date
        """
        return self.date.strftime(format_str)

    def get_document_folder_name(self):
        """
        Get the name of the folder where documents for this engagement should be saved.

        Returns:
            str: The folder name
        """
        if not self.client or not self.client.name:
            return "Unnamed Client - Scoping Documents"

        return f"{self.client.name} - Scoping Documents"

    def get_taci_sow_template_path(self):
        """
        Get the path to the TACI SOW template for this engagement.

        Returns:
            str: The path to the TACI SOW template
        """
        return "templates/base_templates/taci/SOW_Ransom_Consulting_template.docx"

    def get_taci_sow_filename(self):
        """
        Get the filename for the Threat Actor Communications and Intelligence (TACI) SOW document.

        Returns:
            str: The filename for the Threat Actor Communications and Intelligence (TACI) SOW document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Ransom Communications - SOW {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Ransom Communications - SOW {datetime.now().strftime('%Y%m%d')}.docx"

    def get_rr_sow_template_path(self):
        """
        Get the path to the RR SOW template for this engagement.

        Returns:
            str: The path to the RR SOW template
        """
        return "templates/base_templates/rr/SOW-ON-Template_Recovery & Restoration Support.docx"

    def get_rr_sow_filename(self):
        """
        Get the filename for the RR SOW document.

        Returns:
            str: The filename for the RR SOW document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Recovery & Remediation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Recovery & Remediation - SOW {datetime.now().strftime('%Y%m%d')}.docx"












