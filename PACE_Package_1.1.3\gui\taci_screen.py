"""
TACI screen UI for PACE application.
"""

import sys
from PySide6.QtWidgets import (
    Q<PERSON><PERSON>t, QLabel, QLineEdit, QComboBox, QCheckBox, QSpinBox,
    QPushButton, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QRadioButton, QButtonGroup, QApplication
)
from PySide6.QtCore import Qt, Signal

from models.client import Client
from models.taci import TACIEngagement

class TACIScreen(QWidget):
    """
    TACI screen for the PACE application.
    """

    # Signal emitted when the user clicks the Generate Documents button
    generate_clicked = Signal(TACIEngagement)

    # Signal emitted when the user clicks the Back button
    back_clicked = Signal()

    def __init__(self, client):
        """
        Initialize the TACI screen.

        Args:
            client (Client): The client for this engagement
        """
        super().__init__()

        self.client = client
        self.engagement = TACIEngagement(client)

        self.init_ui()

    def init_ui(self):
        """
        Initialize the UI.
        """
        # Set up the main layout
        main_layout = QVBoxLayout()

        # Add the title
        title_label = QLabel("Threat Actor Communications")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Add the client information
        client_info_label = QLabel(f"Client: {self.client.name}")
        client_info_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(client_info_label)

        # Add a separator
        main_layout.addSpacing(20)

        # Create a form layout for the TACI parameters
        form_layout = QFormLayout()

        # Add the service type checkboxes
        services_group = QGroupBox("Service Type")
        services_layout = QVBoxLayout()

        self.ransom_comm_check = QCheckBox("Ransom Communications")
        self.data_download_check = QCheckBox("Data Download")
        self.ransom_site_check = QCheckBox("Ransom Site Monitoring")

        services_layout.addWidget(self.ransom_comm_check)
        services_layout.addWidget(self.data_download_check)
        services_layout.addWidget(self.ransom_site_check)

        services_group.setLayout(services_layout)
        form_layout.addRow("", services_group)

        # Add the MSA checkbox
        self.msa_check = QCheckBox("Generate Master Services Agreement")
        form_layout.addRow("", self.msa_check)

        # Add the data download hours fields
        data_download_group = QGroupBox("Data Download Hours")
        data_download_layout = QHBoxLayout()

        self.data_download_min_spin = QSpinBox()
        self.data_download_min_spin.setRange(1, 1000)
        self.data_download_min_spin.setValue(10)

        self.data_download_max_spin = QSpinBox()
        self.data_download_max_spin.setRange(1, 1000)
        self.data_download_max_spin.setValue(20)

        data_download_layout.addWidget(QLabel("Min:"))
        data_download_layout.addWidget(self.data_download_min_spin)
        data_download_layout.addWidget(QLabel("Max:"))
        data_download_layout.addWidget(self.data_download_max_spin)

        data_download_group.setLayout(data_download_layout)
        form_layout.addRow("", data_download_group)

        # Add the ransom site monitoring hours fields
        ransom_site_group = QGroupBox("Ransom Site Monitoring Hours")
        ransom_site_layout = QHBoxLayout()

        self.ransom_site_min_spin = QSpinBox()
        self.ransom_site_min_spin.setRange(1, 1000)
        self.ransom_site_min_spin.setValue(10)

        self.ransom_site_max_spin = QSpinBox()
        self.ransom_site_max_spin.setRange(1, 1000)
        self.ransom_site_max_spin.setValue(20)

        ransom_site_layout.addWidget(QLabel("Min:"))
        ransom_site_layout.addWidget(self.ransom_site_min_spin)
        ransom_site_layout.addWidget(QLabel("Max:"))
        ransom_site_layout.addWidget(self.ransom_site_max_spin)

        ransom_site_group.setLayout(ransom_site_layout)
        form_layout.addRow("", ransom_site_group)

        # Add the pricing model options
        pricing_group = QGroupBox("Pricing Model")
        pricing_layout = QHBoxLayout()

        self.hourly_radio = QRadioButton("Hourly")
        self.fixed_fee_radio = QRadioButton("Fixed Fee")
        self.hourly_radio.setChecked(True)

        # Add tooltip to explain the difference
        self.hourly_radio.setToolTip("Hourly pricing model with single price per phase (no ranges)")
        self.fixed_fee_radio.setToolTip("Fixed fee pricing model for specific carriers like Coalition")

        pricing_layout.addWidget(self.hourly_radio)
        pricing_layout.addWidget(self.fixed_fee_radio)

        pricing_group.setLayout(pricing_layout)
        form_layout.addRow("", pricing_group)

        # Add the form layout to the main layout
        main_layout.addLayout(form_layout)

        # Add a separator
        main_layout.addSpacing(20)

        # Add the buttons
        button_layout = QHBoxLayout()

        self.back_button = QPushButton("Back")
        self.back_button.clicked.connect(self.on_back_clicked)

        self.generate_button = QPushButton("Generate Documents")
        self.generate_button.clicked.connect(self.on_generate_clicked)

        button_layout.addWidget(self.back_button)
        button_layout.addStretch()
        button_layout.addWidget(self.generate_button)

        main_layout.addLayout(button_layout)

        # Set the main layout
        self.setLayout(main_layout)

        # Set the window properties
        self.setWindowTitle("PACE - Threat Actor Communications")
        self.resize(600, 500)

        # Connect signals
        self.data_download_check.stateChanged.connect(self.on_data_download_check_changed)
        self.ransom_site_check.stateChanged.connect(self.on_ransom_site_check_changed)

        # Initially disable the hours fields
        data_download_group.setEnabled(False)
        ransom_site_group.setEnabled(False)

    def on_data_download_check_changed(self, state):
        """
        Handle changes to the Data Download checkbox.

        Args:
            state (int): The checkbox state
        """
        # Enable/disable the data download hours fields
        self.findChild(QGroupBox, "Data Download Hours").setEnabled(state == Qt.Checked)

    def on_ransom_site_check_changed(self, state):
        """
        Handle changes to the Ransom Site Monitoring checkbox.

        Args:
            state (int): The checkbox state
        """
        # Enable/disable the ransom site monitoring hours fields
        self.findChild(QGroupBox, "Ransom Site Monitoring Hours").setEnabled(state == Qt.Checked)

    def on_back_clicked(self):
        """
        Handle the Back button click.
        """
        self.back_clicked.emit()

    def on_generate_clicked(self):
        """
        Handle the Generate Documents button click.
        """
        # Update the engagement object with the form values
        self.engagement.is_ransom_communications = self.ransom_comm_check.isChecked()
        self.engagement.is_data_download = self.data_download_check.isChecked()
        self.engagement.is_ransom_site_monitoring = self.ransom_site_check.isChecked()
        self.engagement.needs_msa = self.msa_check.isChecked()

        self.engagement.data_download_hours_min = self.data_download_min_spin.value()
        self.engagement.data_download_hours_max = self.data_download_max_spin.value()
        self.engagement.ransom_site_monitoring_hours_min = self.ransom_site_min_spin.value()
        self.engagement.ransom_site_monitoring_hours_max = self.ransom_site_max_spin.value()

        # Always use single price model (no ranges), but track if it's hourly or fixed fee
        self.engagement.is_single_price = True
        self.engagement.is_fixed_fee = self.fixed_fee_radio.isChecked()

        # Emit the generate_clicked signal
        self.generate_clicked.emit(self.engagement)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    client = Client("Test Client", "123 Main St", "AIG", "Baker & Hostetler LLP")
    taci_screen = TACIScreen(client)
    taci_screen.show()
    sys.exit(app.exec())
